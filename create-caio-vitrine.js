const { PrismaClient } = require('@prisma/client');
const { hash } = require('bcryptjs');

const prisma = new PrismaClient();

async function createCaioVitrine() {
  console.log('🚀 Criando organização e vitrine para Caio...');

  try {
    // Verificar se já existe uma organização com slug 'caio'
    let caioOrg = await prisma.organization.findUnique({
      where: { slug: 'caio' }
    });

    if (!caioOrg) {
      // Criar organização para Caio
      caioOrg = await prisma.organization.create({
        data: {
          name: 'Caio Academy',
          slug: 'caio',
          logo: 'https://ui-avatars.com/api/?name=Caio+Academy&background=4f46e5&color=fff',
          createdAt: new Date()
        }
      });
      console.log('✅ Organização "Caio Academy" criada');
    } else {
      console.log('ℹ️ Organização "Caio Academy" já existe');
    }

    // Buscar ou criar usuário admin
    let adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!adminUser) {
      const adminPassword = await hash('admin123', 12);
      adminUser = await prisma.user.create({
        data: {
          name: 'Administrador',
          email: '<EMAIL>',
          role: 'admin',
          emailVerified: true,
          image: 'https://ui-avatars.com/api/?name=Admin&background=4f46e5&color=fff',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      await prisma.account.create({
        data: {
          userId: adminUser.id,
          providerId: 'credential',
          accountId: adminUser.email,
          password: adminPassword,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
      console.log('✅ Usuário admin criado');
    }

    // Verificar se o usuário já é membro da organização
    const existingMember = await prisma.member.findFirst({
      where: {
        organizationId: caioOrg.id,
        userId: adminUser.id
      }
    });

    if (!existingMember) {
      // Criar membro owner para a organização
      await prisma.member.create({
        data: {
          organizationId: caioOrg.id,
          userId: adminUser.id,
          role: 'owner',
          createdAt: new Date()
        }
      });
      console.log('✅ Membro owner criado para a organização');
    }

    // Criar cursos para a organização
    const courseImages = [
      '/images/cards/card1.jpg',
      '/images/cards/card2.jpg',
      '/images/cards/card3.jpg'
    ];

    const courses = [];
    const courseNames = [
      'Curso de JavaScript Avançado',
      'React do Zero ao Profissional',
      'Node.js e APIs RESTful'
    ];

    for (let i = 0; i < courseNames.length; i++) {
      const existingCourse = await prisma.courses.findFirst({
        where: {
          name: courseNames[i],
          organizationId: caioOrg.id
        }
      });

      if (!existingCourse) {
        const course = await prisma.courses.create({
          data: {
            name: courseNames[i],
            logo: courseImages[i],
            organizationId: caioOrg.id,
            createdBy: adminUser.id,
            community: 'Comunidade de Desenvolvedores',
            link: `https://academy.caio.com/curso-${i + 1}`
          }
        });
        courses.push(course);
        console.log(`✅ Curso "${courseNames[i]}" criado`);
      } else {
        courses.push(existingCourse);
        console.log(`ℹ️ Curso "${courseNames[i]}" já existe`);
      }
    }

    // Verificar se já existe uma vitrine para a organização
    const existingVitrine = await prisma.vitrine.findFirst({
      where: {
        organizationId: caioOrg.id,
        status: 'PUBLISHED'
      }
    });

    if (!existingVitrine) {
      // Criar vitrine
      const vitrine = await prisma.vitrine.create({
        data: {
          title: 'Academia de Desenvolvimento Caio',
          description: 'Cursos completos de programação e desenvolvimento web',
          status: 'PUBLISHED',
          visibility: 'PUBLIC',
          bannerImage: '/images/banner1.jpg',
          organizationId: caioOrg.id,
          createdBy: adminUser.id
        }
      });

      // Criar seção da vitrine
      const section = await prisma.vitrineSection.create({
        data: {
          vitrineId: vitrine.id,
          title: 'Cursos de Programação',
          subtitle: 'Aprenda as tecnologias mais demandadas do mercado',
          description: 'Cursos práticos e atualizados para você se tornar um desenvolvedor completo.',
          position: 1,
          isLocked: false,
          requiresPurchase: false,
          accessType: 'FREE',
          visibility: 'PUBLIC'
        }
      });

      // Adicionar cursos à seção
      for (let i = 0; i < courses.length; i++) {
        await prisma.vitrineSectionCourse.create({
          data: {
            sectionId: section.id,
            courseId: courses[i].id,
            position: i + 1
          }
        });
      }

      console.log('✅ Vitrine "Academia de Desenvolvimento Caio" criada com sucesso!');
      console.log(`📊 Vitrine ID: ${vitrine.id}`);
      console.log(`🏢 Organização ID: ${caioOrg.id}`);
      console.log(`📚 ${courses.length} cursos adicionados à vitrine`);
    } else {
      console.log('ℹ️ Vitrine já existe para a organização Caio');
    }

  } catch (error) {
    console.error('❌ Erro ao criar vitrine para Caio:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar o script
createCaioVitrine();