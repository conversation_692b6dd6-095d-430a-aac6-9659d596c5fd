import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { z } from 'zod';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { sendEmail } from '@/packages/mail';
import { nanoid } from 'nanoid';
import crypto from 'crypto';

const caktoWebhookSchema = z.object({
  id: z.string(),
  customer: z.object({
    name: z.string(),
    email: z.string().email(),
    phone: z.string().optional(),
    docNumber: z.string().optional(),
  }),
  product: z.object({
    name: z.string(),
    id: z.string(),
    short_id: z.string().optional(),
  }),
  status: z.enum(['approved', 'pending', 'cancelled', 'refunded']),
  amount: z.number(),
  paymentMethod: z.string(),
  paidAt: z.string().optional(),
  createdAt: z.string(),
});

type CaktoWebhookPayload = z.infer<typeof caktoWebhookSchema>;

function verifyWebhookSignature(payload: string, signature: string, secret: string): boolean {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');

  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
}

export async function caktoWebhookRoutes(fastify: FastifyInstance) {
  fastify.post('/webhooks/cakto/purchase', async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const signature = request.headers['x-cakto-signature'] as string;
      const webhookSecret = process.env.CAKTO_WEBHOOK_SECRET;

      if (!webhookSecret) {
        logger.error('CAKTO_WEBHOOK_SECRET not configured');
        return reply.status(500).send({ error: 'Webhook not configured' });
      }

      if (!signature) {
        logger.error('Missing webhook signature');
        return reply.status(401).send({ error: 'Missing signature' });
      }

      const rawBody = JSON.stringify(request.body);
      if (!verifyWebhookSignature(rawBody, signature, webhookSecret)) {
        logger.error('Invalid webhook signature');
        return reply.status(401).send({ error: 'Invalid signature' });
      }

      const payload = caktoWebhookSchema.parse(request.body);

      logger.info('Received Cakto webhook', {
        orderId: payload.id,
        email: payload.customer.email,
        status: payload.status
      });

      if (payload.status !== 'approved') {
        logger.info('Ignoring non-approved purchase', { status: payload.status });
        return reply.status(200).send({ success: true, message: 'Status ignored' });
      }

      await processPurchaseWebhook(payload);

      return reply.status(200).send({ success: true, message: 'Purchase processed' });

    } catch (error) {
      logger.error('Error processing Cakto webhook:', error);
      return reply.status(500).send({ error: 'Internal server error' });
    }
  });
}

async function processPurchaseWebhook(payload: CaktoWebhookPayload) {
  const { customer, product } = payload;

  let user = await db.user.findUnique({
    where: { email: customer.email }
  });

  let isNewUser = false;
  let generatedPassword: string | null = null;

  if (!user) {
    generatedPassword = nanoid(12);

    user = await db.user.create({
      data: {
        email: customer.email,
        name: customer.name,
        role: 'user',
        emailVerified: true,
        onboardingComplete: false,
      }
    });

    isNewUser = true;
    logger.info('Created new user', { userId: user.id, email: user.email });
  }

  const courseProduct = await db.courseProduct.findFirst({
    where: { caktoProductId: product.id },
    include: { course: true }
  });

  if (!courseProduct) {
    logger.error('No course found for product', { productId: product.id });
    throw new Error(`No course associated with product ${product.id}`);
  }

  const existingAccess = await db.userCourses.findFirst({
    where: {
      userId: user.id,
      courseId: courseProduct.courseId,
    }
  });

  if (!existingAccess) {
    await db.userCourses.create({
      data: {
        userId: user.id,
        courseId: courseProduct.courseId,
        finalTime: null,
      }
    });

    logger.info('Granted course access', {
      userId: user.id,
      courseId: courseProduct.courseId,
      courseName: courseProduct.course.name
    });
  }

  if (isNewUser && generatedPassword) {
    await sendWelcomeEmail(user, generatedPassword);
  }

  await sendCourseAccessEmail(user, courseProduct.course);
}

async function sendWelcomeEmail(user: any, password: string) {
  try {
    await sendEmail({
      to: user.email,
      templateId: "userCreated",
      context: {
        name: user.name,
        email: user.email,
        password,
        url: process.env.NEXT_PUBLIC_APP_URL || "https://members.cakto.com.br",
      },
    });

    logger.info('Welcome email sent', { email: user.email });
  } catch (error) {
    logger.error('Failed to send welcome email:', error);
  }
}

async function sendCourseAccessEmail(user: any, course: any) {
  try {
    await sendEmail({
      to: user.email,
      templateId: "courseAccess",
      context: {
        name: user.name,
        courseName: course.name,
        url: process.env.NEXT_PUBLIC_APP_URL || "https://members.cakto.com.br",
      },
    });

    logger.info('Course access email sent', { email: user.email, course: course.name });
  } catch (error) {
    logger.error('Failed to send course access email:', error);
  }
}
