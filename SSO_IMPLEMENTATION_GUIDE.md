# Guia de Implementação SSO - Members Base

Este documento detalha a implementação do SSO (Single Sign-On) no projeto Members Base, mantendo compatibilidade com as URLs de callback já configuradas no provider SSO da Cakto.

## 📋 Visão Geral

A implementação utiliza o plugin `genericOAuth` do Better Auth para integrar com o SSO Django da Cakto (`sso.cakto.com.br`), mantendo as mesmas URLs de callback já configuradas nos projetos `cakto-members-backend` e `cakto-members-frontend`.

## 🔧 Configurações Necessárias

### 1. Variáveis de Ambiente

Adicione no arquivo `.env`:

```env
# Cakto SSO Configuration
CAKTO_CLIENT_ID=members-area
CAKTO_CLIENT_SECRET=your-secret-here
CAKTO_SSO_URL=https://sso.cakto.com.br
```

### 2. Configuração do Better Auth

Arquivo: `packages/auth/auth.ts`

```typescript
import { genericOAuth } from "better-auth/plugins";

export const auth = betterAuth({
  // ... configurações existentes
  plugins: [
    // ... outros plugins existentes
    genericOAuth({
      config: [
        {
          providerId: "django-sso",
          clientId: process.env.CAKTO_CLIENT_ID || "",
          clientSecret: process.env.CAKTO_CLIENT_SECRET || "",
          discoveryUrl: "https://sso.cakto.com.br/oauth/.well-known/openid-configuration",
          scopes: ["openid", "user"],
          pkce: true,
          responseType: "code",
          mapProfileToUser: (profile: any) => ({
            email: profile.email,
            name: profile.name || profile.preferred_username || profile.email,
            emailVerified: true,
            mainAppUserId: profile.sub || profile.id
          })
        }
      ]
    })
  ],
  // ... resto da configuração
});
```

## 🔄 URLs de Callback

### URLs Mantidas (Compatibilidade)
- **Frontend**: `http://localhost:5173/auth/callback`
- **Backend**: `http://localhost:3001/api/auth/oauth2/callback/django-sso`
- **Produção**: `https://front.members.cakto.app/auth/callback`

### Roteamento no Members Base
O Better Auth automaticamente gerencia o callback através da rota:
- `/api/auth/oauth2/callback/django-sso`

## 📁 Arquivos a Modificar

### 1. CaktoSSOButton Component
Arquivo: `apps/web/modules/saas/auth/components/CaktoSSOButton.tsx`

```typescript
import { authClient } from "@/lib/auth-client";

export function CaktoSSOButton({
  className,
  children,
  onClick,
}: {
  className?: string;
  children?: React.ReactNode;
  onClick?: () => void;
}) {
  const [invitationId] = useQueryState("invitationId", parseAsString);
  const providerData = oAuthProviders.cakto;

  const redirectPath = invitationId
    ? `/app/organization-invitation/${invitationId}`
    : config.auth.redirectAfterSignIn;

  const onSignin = async () => {
    if (onClick) {
      onClick();
    }

    try {
      const callbackURL = new URL(redirectPath, window.location.origin);
      await authClient.signIn.oauth2({
        providerId: "django-sso",
        callbackURL: callbackURL.toString(),
      });
    } catch (error) {
      console.error("Erro ao iniciar SSO:", error);
    }
  };

  return (
    <Button
      onClick={onSignin}
      variant="primary"
      type="button"
      className={cn(className, "bg-[#0F7864] text-white")}
    >
      <Logo color="white" withLabel={false} className="mr-2 size-5" />
      {children || providerData.name}
    </Button>
  );
}
```

### 2. SocialSigninButton Component
Arquivo: `apps/web/modules/saas/auth/components/SocialSigninButton.tsx`

Atualizar o método `onSignin`:

```typescript
const onSignin = async () => {
  if (provider === "cakto") {
    try {
      const callbackURL = new URL(redirectPath, window.location.origin);
      await authClient.signIn.oauth2({
        providerId: "django-sso",
        callbackURL: callbackURL.toString(),
      });
    } catch (error) {
      console.error("Erro ao iniciar SSO Cakto:", error);
    }
    return;
  }

  const callbackURL = new URL(redirectPath, window.location.origin);
  authClient.signIn.social({
    provider: provider as any,
    callbackURL: callbackURL.toString(),
  });
};
```

### 3. Página de Callback (Opcional)
Arquivo: `apps/web/app/(app)/auth/callback/django-sso/page.tsx`

```typescript
"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { LoaderIcon } from "lucide-react";

export default function DjangoSSOCallback() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const error = searchParams.get("error");
    const errorDescription = searchParams.get("error_description");

    if (error) {
      console.error("SSO Error:", error, errorDescription);
      router.push("/auth/login?error=sso_failed");
      return;
    }

    // Better Auth processará automaticamente o callback
    // Aguardar um momento e redirecionar para o dashboard
    const timer = setTimeout(() => {
      router.push("/app");
    }, 2000);

    return () => clearTimeout(timer);
  }, [router, searchParams]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <Card className="w-full max-w-md">
        <CardContent className="flex flex-col items-center justify-center p-6">
          <div className="text-center">
            <LoaderIcon className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
            <h2 className="text-lg font-semibold mb-2">Processando autenticação</h2>
            <p className="text-muted-foreground">
              Aguarde enquanto validamos suas credenciais...
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

## 🔄 Fluxo de Autenticação

1. **Usuário clica no botão SSO** → `CaktoSSOButton` ou `SocialSigninButton` chama `authClient.signIn.oauth2()`
2. **Better Auth redireciona** → `https://sso.cakto.com.br/oauth/authorize/`
3. **Usuário faz login** → Django SSO autentica o usuário
4. **Callback automático** → Better Auth processa automaticamente em `/api/auth/oauth2/callback/django-sso`
5. **Sessão criada** → Better Auth cria sessão e cookies automaticamente
6. **Redirecionamento** → Usuário é redirecionado para a página configurada

## 🗑️ Arquivos a Remover

- `apps/web/app/(app)/auth/sso/callback/page.tsx` (callback manual antigo)

## ✅ Vantagens desta Implementação

1. **Compatibilidade**: Mantém as mesmas URLs de callback já configuradas
2. **Automático**: Better Auth gerencia todo o fluxo OAuth automaticamente
3. **Seguro**: Utiliza PKCE e melhores práticas de segurança
4. **Consistente**: Segue o mesmo padrão dos outros projetos Cakto
5. **Simples**: Menos código customizado para manter

## 🔍 Debugging

Para debugar problemas de SSO:

1. Verificar logs do Better Auth no console
2. Verificar se as variáveis de ambiente estão corretas
3. Verificar se o `client_id` está configurado no Django SSO
4. Verificar se as URLs de callback estão corretas no provider

## 📝 Notas Importantes

- O Better Auth automaticamente gerencia cookies e sessões
- Não é necessário implementar lógica manual de callback
- A página de callback (`/auth/callback/django-sso/page.tsx`) é opcional, apenas para UX
- O `providerId` deve ser sempre `"django-sso"` para manter consistência