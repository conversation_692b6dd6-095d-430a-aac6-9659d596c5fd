import type { VitrineData } from "../types/vitrine";

// Available local images for cycling (same as in seed.ts)
const availableImages = [
  "/images/cards/card1.jpg",
  "/images/cards/card2.jpg",
  "/images/cards/card3.jpg",
  "/images/cards/card4.jpg",
  "/images/cards/card5.jpg",
  "/images/cards/card6.png",
  "/images/cards/card7.png",
  "/images/cards/card8.png",
  "/images/cards/card9.png",
  "/images/cards/card10.png"
];

// Image cycling logic
let imageIndex = 0;
const getNextImage = (): string => {
  const image = availableImages[imageIndex];
  imageIndex = (imageIndex + 1) % availableImages.length;
  return image;
};

const createMockShowcaseItem = (
  id: string,
  title: string,
  description: string,
  category: string,
  level: 'INICIANTE' | 'INTERMEDIARIO' | 'AVANCADO',
  sectionLocked: boolean = false,
  price?: number,
  originalPrice?: number,
  sectionId?: string,
  isContinueWatching?: boolean,
  progressPercentage?: number
) => ({
  id,
  title,
  description,
  category,
  level,
  image: getNextImage(),
  progress: sectionLocked ? 0 : Math.floor(Math.random() * 100),
  duration: `${Math.floor(Math.random() * 20) + 5}h`,
  studentsCount: Math.floor(Math.random() * 10000) + 1000,
  isLocked: sectionLocked,
  sectionLocked,
  price,
  originalPrice,
  discount: originalPrice && price ? Math.round(((originalPrice - price) / originalPrice) * 100) : undefined,
  instructor: ['Caio Martins', 'Ana Silva', 'Pedro Santos', 'Maria Oliveira'][Math.floor(Math.random() * 4)],
  rating: 4.2 + Math.random() * 0.8,
  totalRatings: Math.floor(Math.random() * 5000) + 500,
  tags: ['Marketing Digital', 'Vendas', 'Estratégia', 'Automação', 'Growth'].slice(0, Math.floor(Math.random() * 3) + 2),
  sectionId,
  isContinueWatching,
  progressPercentage
});

const continueWatchingItems = [
  createMockShowcaseItem(
    'cw-1',
    'JavaScript Moderno - ES2024',
    'Novidades e recursos mais recentes do JavaScript',
    'JavaScript',
    'INTERMEDIARIO',
    false,
    undefined,
    undefined,
    'continue-watching',
    true,
    67
  ),
  createMockShowcaseItem(
    'cw-2',
    'React - Fundamentos Completos',
    'Domine React do básico ao avançado com projetos práticos',
    'Programação',
    'INTERMEDIARIO',
    false,
    undefined,
    undefined,
    'continue-watching',
    true,
    34
  )
];

const trilhaProdutosDigitais = [
  createMockShowcaseItem(
    'tpd-1',
    'Trilha de Produtos Digitais a360',
    'Aprenda a criar, vender e escalar produtos digitais do zero ao primeiro milhão',
    'Produtos Digitais',
    'INTERMEDIARIO',
    false,
    undefined,
    undefined,
    'trilha-produtos-digitais'
  ),
  createMockShowcaseItem(
    'tpd-2',
    'Template Conversão para Landing Pages',
    'Templates profissionais para criar landing pages que convertem',
    'Templates',
    'INICIANTE',
    false,
    undefined,
    undefined,
    'trilha-produtos-digitais'
  ),
  createMockShowcaseItem(
    'tpd-3',
    'Microprodutos - Estratégias Avançadas',
    'Como criar e vender microprodutos de alto valor',
    'Estratégia',
    'AVANCADO',
    false,
    undefined,
    undefined,
    'trilha-produtos-digitais'
  ),
  createMockShowcaseItem(
    'tpd-4',
    'Autoridade 100x - Expert sem ser Expert',
    'Construa autoridade digital mesmo sendo iniciante',
    'Autoridade',
    'INTERMEDIARIO',
    false,
    undefined,
    undefined,
    'trilha-produtos-digitais'
  ),
  createMockShowcaseItem(
    'tpd-5',
    'Funil de Vendas Automatizado',
    'Crie funis que vendem 24/7 no piloto automático',
    'Automação',
    'AVANCADO',
    false,
    undefined,
    undefined,
    'trilha-produtos-digitais'
  ),
  createMockShowcaseItem(
    'tpd-6',
    'Copy que Converte - Fórmulas Secretas',
    'Aprenda as fórmulas de copywriting que mais convertem',
    'Copywriting',
    'INTERMEDIARIO',
    false,
    undefined,
    undefined,
    'trilha-produtos-digitais'
  )
];

const founderExpress = [
  createMockShowcaseItem(
    'fe-1',
    'Crie Uma vez, Venda 10 mil vezes',
    'A estratégia definitiva para criar produtos escaláveis',
    'Escalabilidade',
    'AVANCADO',
    false,
    undefined,
    undefined,
    'founder-express'
  ),
  createMockShowcaseItem(
    'fe-2',
    'Autoridade 100x - Como Criar Produtos sem ser Expert',
    'Torne-se uma autoridade no seu nicho rapidamente',
    'Autoridade',
    'INTERMEDIARIO',
    false,
    undefined,
    undefined,
    'founder-express'
  ),
  createMockShowcaseItem(
    'fe-3',
    'Máquina de Produtos Lucrativos',
    'Sistema para criar produtos que realmente vendem',
    'Produtos',
    'AVANCADO',
    false,
    undefined,
    undefined,
    'founder-express'
  ),
  createMockShowcaseItem(
    'fe-4',
    'A Lista - Referências para Duplicar Páginas Milionárias',
    'Modelos de páginas que já faturaram milhões',
    'Referências',
    'INTERMEDIARIO',
    false,
    undefined,
    undefined,
    'founder-express'
  ),
  createMockShowcaseItem(
    'fe-5',
    'Sistema de Lançamentos Lucrativos',
    'Como criar lançamentos que vendem milhões',
    'Lançamentos',
    'AVANCADO',
    false,
    undefined,
    undefined,
    'founder-express'
  )
];

const formacaoPrime = [
  createMockShowcaseItem(
    'fp-1',
    'React Avançado - Hooks e Performance',
    'Domine React Hooks e otimização de performance',
    'React',
    'AVANCADO',
    false,
    undefined,
    undefined,
    'formacao-prime'
  ),
  createMockShowcaseItem(
    'fp-2',
    'Node.js Backend - APIs Escaláveis',
    'Construa APIs robustas e escaláveis com Node.js',
    'Node.js',
    'INTERMEDIARIO',
    false,
    undefined,
    undefined,
    'formacao-prime'
  ),
  createMockShowcaseItem(
    'fp-3',
    'TypeScript - Do Básico ao Avançado',
    'Aprenda TypeScript para projetos profissionais',
    'TypeScript',
    'INTERMEDIARIO',
    false,
    undefined,
    undefined,
    'formacao-prime'
  ),
  createMockShowcaseItem(
    'fp-4',
    'Next.js 14 - Full Stack Development',
    'Desenvolva aplicações full-stack com Next.js 14',
    'Next.js',
    'AVANCADO',
    false,
    undefined,
    undefined,
    'formacao-prime'
  ),
  createMockShowcaseItem(
    'fp-5',
    'Database Design - PostgreSQL e Prisma',
    'Modelagem de banco de dados e ORM com Prisma',
    'Database',
    'INTERMEDIARIO',
    false,
    undefined,
    undefined,
    'formacao-prime'
  )
];

const novosLancamentos = [
  createMockShowcaseItem(
    'nl-1',
    'Marketing Digital do Zero ao PRO',
    'Estratégias completas de marketing digital',
    'Marketing Digital',
    'INICIANTE',
    false,
    undefined,
    undefined,
    'novos-lancamentos'
  ),
  createMockShowcaseItem(
    'nl-2',
    'Funil de Vendas que Converte',
    'Crie funis de vendas que realmente convertem',
    'Vendas',
    'INTERMEDIARIO',
    false,
    undefined,
    undefined,
    'novos-lancamentos'
  ),
  createMockShowcaseItem(
    'nl-3',
    'Facebook Ads Avançado - ROI Garantido',
    'Estratégias avançadas de Facebook Ads',
    'Facebook Ads',
    'AVANCADO',
    true,
    197,
    397,
    'novos-lancamentos'
  ),
  createMockShowcaseItem(
    'nl-4',
    'Google Ads Mastery - Vendas no Automático',
    'Domine o Google Ads e automatize suas vendas',
    'Google Ads',
    'AVANCADO',
    true,
    247,
    497,
    'novos-lancamentos'
  ),
  createMockShowcaseItem(
    'nl-5',
    'E-commerce do Zero ao Milhão',
    'Construa um e-commerce lucrativo do zero',
    'E-commerce',
    'AVANCADO',
    true,
    297,
    597,
    'novos-lancamentos'
  )
];

export const mockVitrineData: VitrineData = {
  title: "Transforme Sua Carreira",
  description: "Acesse conteúdos exclusivos, cursos premium e uma comunidade de empreendedores digitais que estão transformando suas vidas através do conhecimento.",
  bannerImage: "/images/banner1.jpg",
  sections: [
    {
      id: "continue-watching",
      title: "Continue Assistindo",
      subtitle: "Retome seus estudos de onde parou",
      items: continueWatchingItems,
      isLocked: false
    },
    {
      id: "founder-express",
      title: "Founder Express",
      subtitle: "Acelere seu crescimento como empreendedor digital",
      items: founderExpress,
      isLocked: false
    },
    {
      id: "trilha-produtos-digitais",
      title: "Trilha de Produtos Digitais",
      subtitle: "Do zero ao primeiro milhão com produtos digitais",
      items: trilhaProdutosDigitais,
      isLocked: false
    },
    {
      id: "formacao-prime",
      title: "Formação Prime",
      subtitle: "Cursos técnicos para desenvolvedores",
      items: formacaoPrime,
      isLocked: false
    },
    {
      id: "novos-lancamentos",
      title: "Novos Lançamentos",
      subtitle: "Os cursos mais recentes da plataforma",
      items: novosLancamentos,
      isLocked: false,
      checkoutUrl: "https://checkout.cakto.com.br"
    }
  ]
};
