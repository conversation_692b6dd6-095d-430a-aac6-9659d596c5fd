"use client";

import { Card } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Progress } from "@ui/components/progress";
import { Lock, Play, ShoppingCart } from "lucide-react";
import type { ShowcaseItem } from "../../../types/vitrine";
import { motion } from "framer-motion";
import { cn } from "@ui/lib";

interface ShowcaseCardProps {
  item: ShowcaseItem;
  onPlay?: (item: ShowcaseItem) => void;
  onPurchase?: (item: ShowcaseItem) => void;
  className?: string;
}

export function ShowcaseCard({
  item,
  onPlay,
  onPurchase,
  className,
}: ShowcaseCardProps) {
  const handleAction = () => {
    if ((item.sectionLocked || item.isLocked) && onPurchase) {
      onPurchase(item);
    } else if (onPlay) {
      onPlay(item);
    }
  };

  const getButtonText = () => {
    if (item.isContinueWatching) return "Continuar Assistindo";
    if (item.sectionLocked || item.isLocked) return "Comprar";
    if (item.progress > 0) return "Continuar Assistindo";
    return "Assistir";
  };

  const getButtonIcon = () => {
    if (item.sectionLocked || item.isLocked) return ShoppingCart;
    return Play;
  };

  const getButtonVariant = () => {
    if (item.sectionLocked || item.isLocked) return "secondary";
    return "primary";
  };

  const ButtonIcon = getButtonIcon();
  const isLocked = item.sectionLocked || item.isLocked;
  const isContinueWatching = item.isContinueWatching;

  // Use consistent aspect ratio for all cards to maintain visual consistency
  const cardAspectRatio = "aspect-[3/4]";

  return (
    <motion.div
      className={cn(
        "showcase-card-wrapper group cursor-pointer",
        className
      )}
      whileHover={{
        y: -4, // Reduced movement to prevent cropping
        transition: { duration: 0.3, ease: [0.4, 0, 0.2, 1] }
      }}
      onClick={handleAction}
      role="button"
      tabIndex={0}
      aria-label={`${getButtonText()} ${item.title}`}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          handleAction();
        }
      }}
    >
      <Card className={cn(
        "showcase-card bg-card border border-border transition-all duration-300 ease-out",
        "hover:border-primary/50 hover:shadow-xl shadow-lg",
        "focus-within:ring-2 focus-within:ring-primary/20",
        isContinueWatching && "ring-2 ring-primary/30"
      )}>
        {/* Image Container with dynamic aspect ratio and proper overflow handling */}
        <div
          className={cn(
            "showcase-card-image-container",
            cardAspectRatio
          )}
          style={{
            backgroundImage: `url(${item.image})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
          }}
        >
          {/* Hidden img for accessibility and SEO */}
          <img
            src={item.image}
            alt={item.title}
            className="sr-only"
            loading="lazy"
          />

          {/* Lock icon for premium content - Prominent styling matching reference */}
          {isLocked && (
            <div className="absolute top-4 right-4 z-50">
              <div className="bg-background/95 backdrop-blur-sm rounded-full p-3 border-2 border-border shadow-lg">
                <Lock className="w-5 h-5 text-muted-foreground" />
              </div>
            </div>
          )}

          {/* Continue watching progress bar */}
          {isContinueWatching && item.progressPercentage && (
            <div className="absolute top-4 left-4 right-16 z-50">
              <div className="bg-background/95 backdrop-blur-sm rounded-full p-2 border border-border shadow-lg">
                <div className="flex items-center gap-2">
                  <Progress
                    value={item.progressPercentage}
                    className="flex-1 h-1 md:h-2"
                  />
                  <span className="text-xs font-medium text-foreground">
                    {item.progressPercentage}%
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Hover overlay - Styled via CSS */}
          <div className="showcase-card-overlay" />

          {/* Interaction feedback overlay - Styled via CSS */}
          <div className="showcase-card-interaction" />

          {/* Title overlay - ALWAYS VISIBLE */}
          <div className="showcase-card-title">
            <h3 className="text-white font-semibold text-base line-clamp-2 leading-tight drop-shadow-lg">
              {item.title}
            </h3>
          </div>

          {/* Action button overlay - ONLY VISIBLE ON HOVER */}
          <div className="showcase-card-button">
            <h3 className="text-white font-semibold text-base line-clamp-2 leading-tight mb-3 drop-shadow-lg">
              {item.title}
            </h3>

            <Button
              size="sm"
              variant={getButtonVariant()}
              className={cn(
                "w-full transition-all duration-300 ease-out font-medium",
                // Proper secondary theme colors for locked content
                isLocked && "bg-white text-secondary-foreground hover:bg-secondary/80 border-secondary",
                // Primary colors for unlocked content
                !isLocked && "bg-white text-primary-foreground hover:bg-primary/90"
              )}
              onClick={(e) => {
                e.stopPropagation();
                handleAction();
              }}
            >
              <ButtonIcon className="w-4 h-4 mr-2" />
              {getButtonText()}
            </Button>
          </div>
        </div>
      </Card>
    </motion.div>
  );
}
