"use client";

import { useCallback, useEffect, useState } from "react";
import { Button } from "@ui/components/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import type { ShowcaseItem } from "../../../types/vitrine";
import { ShowcaseCard } from "./showcase-card";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@ui/lib";

interface NetflixCarouselProps {
  title: string;
  subtitle?: string;
  items: ShowcaseItem[];
  onPlay?: (item: ShowcaseItem) => void;
  onPurchase?: (item: ShowcaseItem) => void;
  className?: string;
}

export function NetflixCarousel({
  title,
  subtitle,
  items,
  onPlay,
  onPurchase,
  className,
}: NetflixCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [itemsPerView, setItemsPerView] = useState(4);
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(true);
  const [touchStartX, setTouchStartX] = useState<number | null>(null);
  const [touchEndX, setTouchEndX] = useState<number | null>(null);

  // Responsividade
  useEffect(() => {
    const updateItemsPerView = () => {
      const width = window.innerWidth;
      if (width < 640) {
        setItemsPerView(1.2);
      } else if (width < 768) {
        setItemsPerView(2.2);
      } else if (width < 1024) {
        setItemsPerView(3.2);
      } else if (width < 1280) {
        setItemsPerView(4.2);
      } else {
        setItemsPerView(5.2);
      }
    };

    updateItemsPerView();
    window.addEventListener("resize", updateItemsPerView);
    return () => window.removeEventListener("resize", updateItemsPerView);
  }, []);

  // Atualizar botões de navegação
  useEffect(() => {
    setCanScrollPrev(currentIndex > 0);
    setCanScrollNext(
      currentIndex < items.length - Math.floor(itemsPerView),
    );
  }, [currentIndex, itemsPerView, items.length]);

  const scrollPrev = useCallback(() => {
    if (canScrollPrev) {
      setCurrentIndex((prev) => Math.max(0, prev - Math.floor(itemsPerView)));
    }
  }, [canScrollPrev, itemsPerView]);

  const scrollNext = useCallback(() => {
    if (canScrollNext) {
      setCurrentIndex((prev) =>
        Math.min(
          items.length - Math.floor(itemsPerView),
          prev + Math.floor(itemsPerView),
        ),
      );
    }
  }, [canScrollNext, itemsPerView, items.length]);

  // Navegação por teclado
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "ArrowLeft") {
        scrollPrev();
      } else if (e.key === "ArrowRight") {
        scrollNext();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [scrollPrev, scrollNext]);

  // Funções de touch
  function handleTouchStart(e: React.TouchEvent) {
    setTouchStartX(e.touches[0].clientX);
  }
  function handleTouchMove(e: React.TouchEvent) {
    setTouchEndX(e.touches[0].clientX);
  }
  function handleTouchEnd() {
    if (touchStartX !== null && touchEndX !== null) {
      const diff = touchStartX - touchEndX;
      if (diff > 40) scrollNext();
      if (diff < -40) scrollPrev();
    }
    setTouchStartX(null);
    setTouchEndX(null);
  }

  if (!items || items.length === 0) {
    return null;
  }

  return (
    <section className={cn("space-y-3 md:space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-white">
            {title}
          </h2>
          {subtitle && (
            <p className="text-gray-400 text-sm md:text-base">{subtitle}</p>
          )}
        </div>

        {/* Navigation buttons - Desktop only */}
        <div className="hidden md:flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={scrollPrev}
            disabled={!canScrollPrev}
            className={cn(
              "h-10 w-10 rounded-full bg-black/50 hover:bg-black/70 text-white border-0 transition-all duration-300",
              !canScrollPrev && "opacity-50 cursor-not-allowed",
            )}
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={scrollNext}
            disabled={!canScrollNext}
            className={cn(
              "h-10 w-10 rounded-full bg-black/50 hover:bg-black/70 text-white border-0 transition-all duration-300",
              !canScrollNext && "opacity-50 cursor-not-allowed",
            )}
          >
            <ChevronRight className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Carousel */}
      <div className="relative group netflix-carousel-container">
        <div className="overflow-x-auto md:overflow-hidden w-full max-w-full">
          <motion.div
            className="flex gap-2 sm:gap-3 md:gap-4 flex-nowrap"
            style={{ minWidth: 0 }}
            animate={{
              x: `calc(-${currentIndex * (100 / itemsPerView)}% - ${currentIndex * (window.innerWidth < 640 ? 8 : window.innerWidth < 768 ? 12 : 16)}px)`,
            }}
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 30,
            }}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
            {items.map((item) => (
              <div
                key={item.id}
                className="flex-none netflix-carousel-item"
                style={{
                  width: `calc(${100 / itemsPerView}% - ${(window.innerWidth < 640 ? 8 : window.innerWidth < 768 ? 12 : 16) * (itemsPerView - 1) / itemsPerView}px)`,
                  minWidth: window.innerWidth < 640 ? "75vw" : undefined,
                }}
              >
                <ShowcaseCard
                  item={item}
                  onPlay={onPlay}
                  onPurchase={onPurchase}
                />
              </div>
            ))}
          </motion.div>
        </div>

        {/* Hover navigation - Desktop */}
        <AnimatePresence>
          {canScrollPrev && (
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              whileHover={{ scale: 1.1 }}
              onClick={scrollPrev}
              className="absolute left-2 top-1/2 -translate-y-1/2 z-10 hidden md:flex items-center justify-center w-12 h-12 bg-black/70 hover:bg-black/90 text-white rounded-full transition-all duration-300 opacity-0 group-hover:opacity-100"
            >
              <ChevronLeft className="h-6 w-6" />
            </motion.button>
          )}
        </AnimatePresence>

        <AnimatePresence>
          {canScrollNext && (
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              whileHover={{ scale: 1.1 }}
              onClick={scrollNext}
              className="absolute right-2 top-1/2 -translate-y-1/2 z-10 hidden md:flex items-center justify-center w-12 h-12 bg-black/70 hover:bg-black/90 text-white rounded-full transition-all duration-300 opacity-0 group-hover:opacity-100"
            >
              <ChevronRight className="h-6 w-6" />
            </motion.button>
          )}
        </AnimatePresence>
      </div>

      {/* Progress indicators */}
      <div className="flex justify-center gap-2 mt-3 md:mt-4">
        {Array.from({
          length: Math.ceil(items.length / Math.floor(itemsPerView)),
        }).map((_, index) => (
          <button
            key={index}
            onClick={() =>
              setCurrentIndex(index * Math.floor(itemsPerView))
            }
            className={cn(
              "w-2 h-2 rounded-full transition-all duration-300",
              Math.floor(currentIndex / Math.floor(itemsPerView)) === index
                ? "bg-purple-500 w-6"
                : "bg-gray-600 hover:bg-gray-500",
            )}
          />
        ))}
      </div>
    </section>
  );
}
