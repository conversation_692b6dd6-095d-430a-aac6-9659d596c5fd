import { auth } from "@repo/auth";
import type { Session } from "@repo/auth";
import type { NextRequest } from "next/server";
import { cookies, headers } from "next/headers";

// For middleware use
export const getSessionFromRequest = async (req: NextRequest): Promise<Session | null> => {
	try {
		// Construct the URL safely
		const origin = req.nextUrl.origin || `${req.nextUrl.protocol}//${req.nextUrl.host}` || 'http://localhost:3000';
		const url = new URL("/api/auth/get-session?disableCookieCache=true", origin);

		const response = await fetch(url, {
			headers: {
				cookie: req.headers.get("cookie") || "",
			},
		});

		if (!response.ok) {
			return null;
		}

		const session = await response.json();
		return session;
	} catch (error) {
		console.error("Error fetching session:", error);
		return null;
	}
};

// For server components use
export const getSession = async (
	headersList: Headers,
	cookieStore: ReturnType<typeof cookies>
): Promise<Session | null> => {
	try {
		// Use better-auth's built-in API for server components
		const session = await auth.api.getSession({
			headers: headersList,
		});

		return session;
	} catch (error) {
		console.error("Error fetching session:", error);
		return null;
	}
};

export const getOrganizationsForSession = async (
	req: NextRequest,
) => {
	try {
		// Construct the URL safely
		const origin = req.nextUrl.origin || `${req.nextUrl.protocol}//${req.nextUrl.host}` || 'http://localhost:3000';
		const url = new URL("/api/auth/organization/list", origin);

		const response = await fetch(url, {
			headers: {
				cookie: req.headers.get("cookie") || "",
			},
		});

		if (!response.ok) {
			return [];
		}

		return (await response.json()) ?? [];
	} catch (error) {
		console.error("Error fetching organizations:", error);
		return [];
	}
};
