import { db } from "@repo/database";

export async function getOrganizationByDomain(host: string) {
  // Check for custom domain first
  let organization = await db.organization.findFirst({
    where: { customDomain: host },
    include: { members: true, invitations: true },
  });

  if (organization) {
    return organization;
  }

  // Check for subdomain
  const subdomain = extractSubdomain(host);
  if (subdomain) {
    organization = await db.organization.findFirst({
      where: { subdomain },
      include: { members: true, invitations: true },
    });
  }

  return organization;
}

export function extractSubdomain(host: string): string | null {
  const parts = host.split('.');
  
  // For cakto.com.br subdomains
  if (parts.length >= 4 && parts.slice(-3).join('.') === 'cakto.com.br') {
    return parts[0];
  }
  
  return null;
}

export function isDomainBasedRequest(host: string): boolean {
  return extractSubdomain(host) !== null || isCustomDomain(host);
}

export function isCustomDomain(host: string): boolean {
  return !host.includes('cakto.com.br') && !host.includes('localhost');
}