import { VitrineConfig, ShowcaseItem } from "../types";

export const mockVitrineData: VitrineConfig = {
  id: "1",
  title: "Plataforma de Cursos Online",
  description: "Sua jornada de aprendizado começa aqui",
  visibility: "public",
  bannerImage: "/images/vitrine/banner1.jpg",
  sections: [
    {
      id: "section-1",
      title: "Cursos em Destaque",
      subtitle: "Os mais populares da plataforma",
      position: 1,
      items: [
        {
          id: "course-1",
          title: "React Avançado",
          image: "/images/vitrine/cards/card1.jpg",
          description: "Aprenda React do zero ao avançado",
          category: "Desenvolvimento",
          progress: 0,
          duration: "2h 30min",
          studentsCount: 1500,
          rating: 4.8,
          totalRatings: 245,
          level: "AVANCADO",
          instructor: "<PERSON>",
          tags: ["React", "JavaScript", "Frontend"],
        },
        {
          id: "course-2",
          title: "Node.js Backend",
          image: "/images/vitrine/cards/card2.jpg",
          description: "Construa APIs robustas com Node.js",
          category: "Desenvolvimento",
          progress: 0,
          duration: "3h 15min",
          studentsCount: 1200,
          rating: 4.9,
          totalRatings: 189,
          level: "INTERMEDIARIO",
          instructor: "Maria Santos",
          tags: ["Node.js", "JavaScript", "Backend"],
        },
        {
          id: "course-3",
          title: "Python para Data Science",
          image: "/images/vitrine/cards/card3.jpg",
          description: "Análise de dados com Python",
          category: "Data Science",
          progress: 0,
          duration: "4h 45min",
          studentsCount: 2100,
          rating: 4.7,
          totalRatings: 312,
          level: "INICIANTE",
          instructor: "Carlos Oliveira",
          tags: ["Python", "Data Science", "Machine Learning"],
        },
        {
          id: "course-4",
          title: "UX/UI Design",
          image: "/images/vitrine/cards/card4.jpg",
          description: "Design de interfaces modernas",
          category: "Design",
          progress: 0,
          duration: "2h 55min",
          studentsCount: 980,
          rating: 4.6,
          totalRatings: 156,
          level: "INTERMEDIARIO",
          instructor: "Ana Costa",
          tags: ["UX", "UI", "Design", "Figma"],
        },
        {
          id: "course-5",
          title: "DevOps Essentials",
          image: "/images/vitrine/cards/card5.jpg",
          description: "Infraestrutura como código",
          category: "DevOps",
          progress: 0,
          duration: "3h 30min",
          studentsCount: 750,
          rating: 4.5,
          totalRatings: 98,
          level: "AVANCADO",
          instructor: "Pedro Lima",
          tags: ["DevOps", "Docker", "Kubernetes"],
        },
      ],
      isLocked: false,
      requiresPurchase: false,
      accessType: "free",
      visibility: "public",
    },
    {
      id: "section-2",
      title: "Cursos Premium",
      subtitle: "Conteúdo exclusivo para membros",
      position: 2,
      items: [
        {
          id: "course-6",
          title: "Arquitetura de Software",
          image: "/images/vitrine/cards/card6.jpg",
          description: "Padrões e práticas avançadas",
          category: "Arquitetura",
          progress: 0,
          duration: "5h 20min",
          studentsCount: 450,
          rating: 4.9,
          totalRatings: 67,
          level: "AVANCADO",
          instructor: "Roberto Alves",
          tags: ["Arquitetura", "Clean Code", "SOLID"],
          isLocked: true,
          price: 199.99,
          originalPrice: 299.99,
          discount: 33,
        },
        {
          id: "course-7",
          title: "Microserviços",
          image: "/images/vitrine/cards/card7.jpg",
          description: "Construa sistemas escaláveis",
          category: "Arquitetura",
          progress: 0,
          duration: "4h 15min",
          studentsCount: 320,
          rating: 4.8,
          totalRatings: 45,
          level: "AVANCADO",
          instructor: "Fernanda Silva",
          tags: ["Microserviços", "API Gateway", "Docker"],
          isLocked: true,
          price: 179.99,
          originalPrice: 249.99,
          discount: 28,
        },
        {
          id: "course-8",
          title: "Machine Learning",
          image: "/images/vitrine/cards/card8.jpg",
          description: "Algoritmos e modelos preditivos",
          category: "Machine Learning",
          progress: 0,
          duration: "6h 45min",
          studentsCount: 280,
          rating: 4.7,
          totalRatings: 38,
          level: "AVANCADO",
          instructor: "Lucas Mendes",
          tags: ["Machine Learning", "Python", "TensorFlow"],
          isLocked: true,
          price: 249.99,
          originalPrice: 349.99,
          discount: 29,
        },
      ],
      isLocked: true,
      requiresPurchase: true,
      checkoutUrl: "https://checkout.example.com/premium",
      accessType: "paid",
      visibility: "public",
      price: 499.99,
      originalPrice: 699.99,
    },
    {
      id: "section-3",
      title: "Continuar Assistindo",
      subtitle: "Retome de onde parou",
      position: 3,
      items: [
        {
          id: "course-1",
          title: "React Avançado",
          image: "/images/vitrine/cards/card1.jpg",
          description: "Aprenda React do zero ao avançado",
          category: "Desenvolvimento",
          progress: 45,
          duration: "2h 30min",
          studentsCount: 1500,
          rating: 4.8,
          totalRatings: 245,
          level: "AVANCADO",
          instructor: "João Silva",
          tags: ["React", "JavaScript", "Frontend"],
          isContinueWatching: true,
          progressPercentage: 45,
        },
        {
          id: "course-3",
          title: "Python para Data Science",
          image: "/images/vitrine/cards/card3.jpg",
          description: "Análise de dados com Python",
          category: "Data Science",
          progress: 78,
          duration: "4h 45min",
          studentsCount: 2100,
          rating: 4.7,
          totalRatings: 312,
          level: "INICIANTE",
          instructor: "Carlos Oliveira",
          tags: ["Python", "Data Science", "Machine Learning"],
          isContinueWatching: true,
          progressPercentage: 78,
        },
      ],
      isLocked: false,
      requiresPurchase: false,
      accessType: "free",
      visibility: "public",
    },
  ],
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z",
  stats: {
    views: 15000,
    enrollments: 8500,
    revenue: 125000,
  },
};

export const mockUserAccess: Record<string, boolean> = {
  "course-1": true,
  "course-2": true,
  "course-3": true,
  "course-4": true,
  "course-5": true,
  "course-6": false,
  "course-7": false,
  "course-8": false,
};
