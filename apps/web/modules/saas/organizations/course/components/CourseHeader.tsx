'use client'

import { But<PERSON> } from '@/modules/ui/components/button'
import { Badge } from '@/modules/ui/components/badge'
import {
  ArrowLeft,
  BookOpen,
  Clock,
  Menu,
  ChevronLeft,
  ChevronRight,
  Users
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { Course, Module, Lesson } from '../types'

interface CourseHeaderProps {
  course: Course
  currentLesson?: Lesson
  currentModule?: Module
  onToggleSidebar: () => void
  sidebarOpen: boolean
  onNext?: () => void
  onPrevious?: () => void
  hasNext?: boolean
  hasPrevious?: boolean
}

export function CourseHeader({
  course,
  currentLesson,
  currentModule,
  onToggleSidebar,
  sidebarOpen,
  onNext,
  onPrevious,
  hasNext,
  hasPrevious
}: CourseHeaderProps) {
  return (
    <header className="flex-shrink-0 border-b border-border/50 bg-background/95 backdrop-blur-sm">
      <div className="p-4">
        <div className="flex items-center justify-between">
          {/* Left side - Course info */}
          <div className="flex items-center gap-2 md:gap-4 min-w-0 flex-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.history.back()}
              className="flex-shrink-0"
            >
              <ArrowLeft className="h-4 w-4 md:mr-2" />
              <span className="hidden md:inline">Voltar</span>
            </Button>

            {course && (
              <div className="flex items-center gap-2 min-w-0">
                <BookOpen className="h-4 w-4 md:h-5 md:w-5 text-muted-foreground flex-shrink-0" />
                <span className="font-semibold truncate text-sm md:text-base">
                  {course.name}
                </span>
              </div>
            )}

            {currentModule && currentLesson && (
              <div className="hidden sm:flex items-center gap-2 min-w-0">
                <span className="text-muted-foreground">/</span>
                <span className="text-muted-foreground truncate text-sm">
                  {currentModule.name}
                </span>
                <span className="text-muted-foreground">/</span>
                <span className="font-medium truncate text-sm">
                  {currentLesson.name}
                </span>
              </div>
            )}
          </div>

          {/* Right side - Controls */}
          <div className="flex items-center gap-1 md:gap-2 flex-shrink-0">
            {/* Lesson info badges */}
            {currentLesson && (
              <div className="hidden lg:flex items-center gap-2 mr-4">
                                 {currentLesson.isCompleted && (
                   <Badge status="success" className="bg-green-100 text-green-800">
                     Concluída
                   </Badge>
                 )}
                 {currentLesson.lessonDuration && (
                   <div className="flex items-center gap-1 px-3 py-1 text-xs border rounded-full bg-background">
                     <Clock className="h-3 w-3" />
                     {currentLesson.lessonDuration}
                   </div>
                 )}
              </div>
            )}

            {/* Navigation buttons */}
            <Button
              variant="outline"
              size="sm"
              onClick={onPrevious}
              disabled={!hasPrevious}
              className="px-2 md:px-3"
            >
              <ChevronLeft className="h-4 w-4 md:mr-1" />
              <span className="hidden md:inline">Anterior</span>
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={onNext}
              disabled={!hasNext}
              className="px-2 md:px-3"
            >
              <span className="hidden md:inline">Próxima</span>
              <ChevronRight className="h-4 w-4 md:ml-1" />
            </Button>

            {/* Sidebar toggle for mobile */}
            <Button
              variant="outline"
              size="sm"
              onClick={onToggleSidebar}
              className={cn(
                "px-2 md:px-3 lg:hidden",
                sidebarOpen && "bg-accent"
              )}
            >
              <Menu className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Mobile lesson info */}
        {currentLesson && (
          <div className="mt-3 sm:hidden">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              {currentModule && (
                <>
                  <span>{currentModule.name}</span>
                  <span>/</span>
                </>
              )}
              <span className="font-medium text-foreground">
                {currentLesson.name}
              </span>
            </div>

            <div className="flex items-center gap-2 mt-1">
                             {currentLesson.isCompleted && (
                 <Badge status="success" className="bg-green-100 text-green-800 text-xs">
                   Concluída
                 </Badge>
               )}
               {currentLesson.lessonDuration && (
                 <div className="flex items-center gap-1 px-3 py-1 text-xs border rounded-full bg-background">
                   <Clock className="h-3 w-3" />
                   {currentLesson.lessonDuration}
                 </div>
               )}
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
