"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/modules/ui/components/button";
import {
	ChevronDown,
	ChevronRight,
	Play,
	CheckCircle,
	X,
	Menu,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Module, Lesson, CourseProgress } from "../types";
import { motion, AnimatePresence } from "framer-motion";
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "@/modules/ui/components/collapsible";

interface CourseSidebarProps {
	modules: Module[];
	lessons: Lesson[];
	allModuleLessons: Record<string, Lesson[]>;
	currentModuleId?: string;
	currentLessonId?: string;
	isOpen: boolean;
	onToggle: () => void;
	onModuleSelect: (moduleId: string) => void;
	onLessonSelect: (lessonId: string, moduleId: string) => void;
	courseProgress: CourseProgress;
	className?: string;
}

export function CourseSidebar({
	modules,
	allModuleLessons,
	currentModuleId,
	currentLessonId,
	isOpen,
	onToggle,
	onModuleSelect,
	onLessonSelect,
	courseProgress,
	className,
}: CourseSidebarProps) {
	const [expandedModules, setExpandedModules] = useState<Set<string>>(
		new Set(currentModuleId ? [currentModuleId] : []),
	);
	const scrollContainerRef = useRef<HTMLDivElement>(null);
	const currentLessonRef = useRef<HTMLButtonElement>(null);

	const toggleModule = (moduleId: string) => {
		const newExpanded = new Set(expandedModules);
		if (newExpanded.has(moduleId)) {
			newExpanded.delete(moduleId);
		} else {
			newExpanded.add(moduleId);
		}
		setExpandedModules(newExpanded);
	};

	// Scroll to current lesson when it changes
	useEffect(() => {
		if (currentLessonRef.current && scrollContainerRef.current) {
			const lessonElement = currentLessonRef.current;
			const container = scrollContainerRef.current;

			// Check if the lesson is visible in the container
			const containerRect = container.getBoundingClientRect();
			const lessonRect = lessonElement.getBoundingClientRect();

			const isVisible =
				lessonRect.top >= containerRect.top &&
				lessonRect.bottom <= containerRect.bottom;

			if (!isVisible) {
				lessonElement.scrollIntoView({
					behavior: "smooth",
					block: "center",
				});
			}
		}
	}, [currentLessonId]);

	// Ensure current module is expanded when currentModuleId changes
	useEffect(() => {
		if (currentModuleId && !expandedModules.has(currentModuleId)) {
			setExpandedModules((prev) => new Set(Array.from(prev).concat(currentModuleId)));
		}
	}, [currentModuleId, expandedModules]);

	const formatDuration = (duration?: string) => {
		if (!duration) return "";
		return duration;
	};

	const getLessonProgress = (lesson: Lesson) => {
		if (lesson.userWatchedLessons?.isCompleted) return 100;
		if (lesson.userWatchedLessons?.currentTime && lesson.duration) {
			const current =
				parseFloat(
					lesson.userWatchedLessons.currentTime.split(":")[0],
				) *
					60 +
				parseFloat(lesson.userWatchedLessons.currentTime.split(":")[1]);
			const total =
				parseFloat(lesson.duration.split(":")[0]) * 60 +
				parseFloat(lesson.duration.split(":")[1]);
			return Math.round((current / total) * 100);
		}
		return 0;
	};

	return (
		<>
			{/* Mobile Overlay */}
			<AnimatePresence>
				{isOpen && (
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						exit={{ opacity: 0 }}
						className="fixed w-full inset-0 bg-black/50 z-40 lg:hidden"
						onClick={onToggle}
					/>
				)}
			</AnimatePresence>

			{/* Sidebar */}
			<motion.aside
				initial={false}
				animate={{
					x: isOpen ? 0 : "100%",
					width: isOpen ? "400px" : "0px",
				}}
				transition={{
					type: "spring",
					stiffness: 300,
					damping: 30,
				}}
				className={cn(
					"fixed right-0 top-16 h-[calc(100vh-4rem)] bg-background/95 backdrop-blur-sm border-l border-border/50 z-50 overflow-hidden",
					"lg:relative lg:z-auto lg:top-0 lg:h-full lg:translate-x-0 lg:w-full lg:flex-shrink-0 lg:bg-background lg:backdrop-blur-none",
					className,
				)}
			>
				<div className="flex flex-col h-full">
					{/* Header */}
					<div className="flex-shrink-0 flex items-center justify-between px-4 py-4 h-16 border-b border-border">
						<div className="flex-1 min-w-0">
							<h2 className="text-lg font-semibold truncate">
								Conteúdo do Curso
							</h2>
						</div>
						<Button
							variant="ghost"
							size="sm"
							onClick={onToggle}
							className="lg:hidden flex-shrink-0"
						>
							<X className="h-4 w-4" />
						</Button>
					</div>

					{/* Modules List */}
					<div
						ref={scrollContainerRef}
						className="flex-1 overflow-y-auto sidebar-scrollbar"
					>
						<div className="py-3 space-y-3">
							{modules.length === 0 ? (
								<div className="px-4 py-8 text-center text-muted-foreground">
									<p>Nenhum módulo encontrado</p>
								</div>
							) : (
								modules.map((module) => {
								const isExpanded = expandedModules.has(
									module.id,
								);
								const isCurrentModule =
									module.id === currentModuleId;
								const moduleLessons =
									allModuleLessons[module.id] || [];
								const moduleProgress =
									moduleLessons.length > 0
										? Math.round(
												(moduleLessons.filter(
													(l) =>
														l.userWatchedLessons
															?.isCompleted,
												).length /
													moduleLessons.length) *
													100,
											)
										: 0;

								return (
									<div
										key={module.id}
										className={cn(
											"mx-3 border rounded-lg bg-card transition-all duration-200",
											isCurrentModule &&
												"border-primary/30 bg-primary/5",
											!isCurrentModule &&
												"border-border/50",
										)}
									>
										<Collapsible open={isExpanded}>
											<CollapsibleTrigger asChild>
												<Button
													variant="ghost"
													className="w-full justify-between p-4 h-auto text-left hover:bg-transparent transition-all duration-200 rounded-lg"
													onClick={() => {
														onModuleSelect(
															module.id,
														);
														toggleModule(module.id);
													}}
												>
													<div className="flex items-center gap-3 flex-1 min-w-0">
														<div className="flex items-center justify-center w-8 h-8 rounded-lg bg-primary/10 text-primary font-bold text-sm flex-shrink-0">
															{module.position}
														</div>

														<div className="flex-1 min-w-0">
															<div className="flex items-center gap-2">
																<span className="font-medium text-sm truncate">
																	{
																		module.name
																	}
																</span>
																{moduleProgress ===
																	100 && (
																	<CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
																)}
															</div>
															<div className="text-xs text-muted-foreground mt-0.5">
																{
																	moduleLessons.length
																}{" "}
																aulas •{" "}
																{formatDuration(
																	module.totalLessonsDuration,
																)}
															</div>
														</div>
													</div>

													<div className="flex items-center gap-2 flex-shrink-0">
														{moduleProgress > 0 &&
															moduleProgress <
																100 && (
																<span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
																	{
																		moduleProgress
																	}
																	%
																</span>
															)}
														{isExpanded ? (
															<ChevronDown className="h-4 w-4 text-muted-foreground" />
														) : (
															<ChevronRight className="h-4 w-4 text-muted-foreground" />
														)}
													</div>
												</Button>
											</CollapsibleTrigger>

											{/* Module Lessons */}
											<CollapsibleContent>
												<div className="border-t border-border/30 bg-muted/20">
													<AnimatePresence>
														{moduleLessons.map(
															(lesson, index) => {
																const isCurrentLesson =
																	lesson.id ===
																	currentLessonId;
																const lessonProgress =
																	getLessonProgress(
																		lesson,
																	);
																const isCompleted =
																	lesson
																		.userWatchedLessons
																		?.isCompleted;

																return (
																	<motion.div
																		key={
																			lesson.id
																		}
																		initial={{
																			opacity: 0,
																			height: 0,
																		}}
																		animate={{
																			opacity: 1,
																			height: "auto",
																		}}
																		exit={{
																			opacity: 0,
																			height: 0,
																		}}
																		transition={{
																			duration: 0.2,
																			delay:
																				index *
																				0.05,
																		}}
																		className={cn(
																			"border-b border-border/20 last:border-b-0",
																		)}
																	>
																		<Button
																			ref={
																				isCurrentLesson
																					? currentLessonRef
																					: undefined
																			}
																			variant="ghost"
																			className={cn(
																				"w-full justify-start p-3 h-auto text-left group transition-all duration-200 rounded-none",
																				"hover:bg-background/50",
																				isCurrentLesson &&
																					"bg-primary/10",
																			)}
																			onClick={() =>
																				onLessonSelect(
																					lesson.id,
																					module.id,
																				)
																			}
																		>
																			<div className="flex items-center gap-3 w-full">
																				{/* Thumbnail */}
																				<div className="relative flex-shrink-0">
																					{lesson.thumbnail ? (
																						<div className="relative">
																							<img
																								src={
																									lesson.thumbnail
																								}
																								alt={
																									lesson.name
																								}
																								className={cn(
																									"w-12 h-8 rounded object-cover border",
																									isCompleted
																										? "border-green-500/50"
																										: "border-border/50",
																								)}
																							/>

																							{/* Progress overlay */}
																							{lessonProgress >
																								0 &&
																								lessonProgress <
																									100 && (
																									<div className="absolute bottom-0 left-0 right-0 h-0.5 bg-black/60 overflow-hidden">
																										<div
																											className="h-full bg-primary transition-all duration-300"
																											style={{
																												width: `${lessonProgress}%`,
																											}}
																										/>
																									</div>
																								)}

																							{/* Play icon overlay */}
																							{!isCompleted && (
																								<div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity">
																									<Play className="h-3 w-3 text-white fill-white" />
																								</div>
																							)}

																							{/* Completion badge */}
																							{isCompleted && (
																								<div className="absolute -top-1 -right-1">
																									<CheckCircle className="h-3 w-3 text-green-500 bg-background rounded-full" />
																								</div>
																							)}
																						</div>
																					) : (
																						<div className="w-12 h-8 rounded bg-muted/50 border border-border/50 flex items-center justify-center">
																							<Play className="h-3 w-3 text-muted-foreground" />
																						</div>
																					)}
																				</div>

																				{/* Lesson Title */}
																				<div className="flex-1 min-w-0">
																					<span
																						className={cn(
																							"text-sm font-medium truncate block",
																							isCurrentLesson &&
																								"text-primary",
																							isCompleted &&
																								"text-muted-foreground",
																						)}
																					>
																						{
																							lesson.name
																						}
																					</span>
																				</div>
																			</div>
																		</Button>
																	</motion.div>
																);
															},
														)}
													</AnimatePresence>
												</div>
											</CollapsibleContent>
										</Collapsible>
									</div>
								);
							})
							)}
						</div>
					</div>
				</div>
			</motion.aside>
		</>
	);
}
