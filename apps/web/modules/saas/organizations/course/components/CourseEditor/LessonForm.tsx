'use client'

import { useState, useEffect } from 'react'
import { Lesson, LessonFormData } from '../../types'
import { Button } from '@/modules/ui/components/button'
import { Input } from '@/modules/ui/components/input'
import { Textarea } from '@/modules/ui/components/textarea'
import { Label } from '@/modules/ui/components/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/modules/ui/components/select'
import { Switch } from '@/modules/ui/components/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/modules/ui/components/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/modules/ui/components/card'
import { Upload, X, Video, FileText, Image as ImageIcon, ExternalLink } from 'lucide-react'
import { cn } from '@/modules/ui/lib'

interface LessonFormProps {
  lesson?: Lesson | null
  onSave: (data: LessonFormData) => void
  onCancel: () => void
}

export function LessonForm({ lesson, onSave, onCancel }: LessonFormProps) {
  const [formData, setFormData] = useState<LessonFormData>({
    name: '',
    description: '',
    videoUrl: '',
    thumbnail: undefined,
    status: 'draft',
    duration: ''
  })
  const [thumbnailPreview, setThumbnailPreview] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [showVideoLibrary, setShowVideoLibrary] = useState(false)

  useEffect(() => {
    if (lesson) {
      setFormData({
        name: lesson.name || '',
        description: lesson.description || '',
        videoUrl: lesson.videoUrl || '',
        thumbnail: undefined,
        status: lesson.status || 'draft',
        duration: lesson.duration || ''
      })
      setThumbnailPreview(lesson.thumbnail || null)
    }
  }, [lesson])

  const handleInputChange = (field: keyof LessonFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleThumbnailUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setErrors(prev => ({ ...prev, thumbnail: 'Por favor, selecione uma imagem válida' }))
      return
    }

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      setErrors(prev => ({ ...prev, thumbnail: 'A imagem deve ter no máximo 2MB' }))
      return
    }

    setFormData(prev => ({ ...prev, thumbnail: file }))
    
    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      setThumbnailPreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)
    
    if (errors.thumbnail) {
      setErrors(prev => ({ ...prev, thumbnail: '' }))
    }
  }

  const handleRemoveThumbnail = () => {
    setFormData(prev => ({ ...prev, thumbnail: undefined }))
    setThumbnailPreview(null)
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Nome da aula é obrigatório'
    }

    if (formData.name.trim().length < 3) {
      newErrors.name = 'Nome deve ter pelo menos 3 caracteres'
    }

    if (formData.description && formData.description.length > 1000) {
      newErrors.description = 'Descrição deve ter no máximo 1000 caracteres'
    }

    if (formData.videoUrl && !isValidUrl(formData.videoUrl)) {
      newErrors.videoUrl = 'URL do vídeo inválida'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setIsLoading(true)
    try {
      await onSave(formData)
    } catch (error) {
      console.error('Error saving lesson:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic">Informações Básicas</TabsTrigger>
          <TabsTrigger value="video">Vídeo</TabsTrigger>
          <TabsTrigger value="files">Arquivos</TabsTrigger>
        </TabsList>
        
        <TabsContent value="basic" className="space-y-4">
          {/* Lesson Name */}
          <div className="space-y-2">
            <Label htmlFor="lesson-name">Nome da Aula *</Label>
            <Input
              id="lesson-name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Ex: Introdução ao módulo"
              className={cn(errors.name && "border-red-500")}
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name}</p>
            )}
          </div>

          {/* Lesson Description */}
          <div className="space-y-2">
            <Label htmlFor="lesson-description">Descrição</Label>
            <Textarea
              id="lesson-description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Descreva o conteúdo desta aula..."
              rows={4}
              className={cn(errors.description && "border-red-500")}
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>Opcional</span>
              <span>{formData.description.length}/1000</span>
            </div>
            {errors.description && (
              <p className="text-sm text-red-600">{errors.description}</p>
            )}
          </div>

          {/* Status */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <Label>Status da Aula</Label>
              <p className="text-sm text-gray-600">Controle se a aula está visível para os alunos</p>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Rascunho</span>
              <Switch
                checked={formData.status === 'published'}
                onCheckedChange={(checked) => 
                  handleInputChange('status', checked ? 'published' : 'draft')
                }
              />
              <span className="text-sm text-gray-600">Publicado</span>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="video" className="space-y-4">
          {/* Video URL */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="video-url">URL do Vídeo</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowVideoLibrary(true)}
              >
                <Video className="h-4 w-4 mr-2" />
                Biblioteca de Vídeos
              </Button>
            </div>
            <Input
              id="video-url"
              value={formData.videoUrl}
              onChange={(e) => handleInputChange('videoUrl', e.target.value)}
              placeholder="https://..."
              className={cn(errors.videoUrl && "border-red-500")}
            />
            {errors.videoUrl && (
              <p className="text-sm text-red-600">{errors.videoUrl}</p>
            )}
          </div>

          {/* Duration */}
          <div className="space-y-2">
            <Label htmlFor="duration">Duração</Label>
            <Input
              id="duration"
              value={formData.duration}
              onChange={(e) => handleInputChange('duration', e.target.value)}
              placeholder="Ex: 15:30"
            />
            <p className="text-xs text-gray-500">Formato: MM:SS ou HH:MM:SS</p>
          </div>

          {/* Thumbnail */}
          <div className="space-y-2">
            <Label>Thumbnail da Aula</Label>
            <div className="space-y-3">
              {thumbnailPreview ? (
                <div className="relative">
                  <img
                    src={thumbnailPreview}
                    alt="Preview do thumbnail"
                    className="w-full h-32 object-cover rounded-lg border"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    onClick={handleRemoveThumbnail}
                    className="absolute top-2 right-2 h-6 w-6 p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ) : (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleThumbnailUpload}
                    className="hidden"
                    id="thumbnail-upload"
                  />
                  <label htmlFor="thumbnail-upload" className="cursor-pointer">
                    <div className="flex flex-col items-center gap-2">
                      <div className="p-2 bg-gray-100 rounded-full">
                        <ImageIcon className="h-6 w-6 text-gray-400" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">Clique para fazer upload</p>
                        <p className="text-xs text-gray-500">PNG, JPG até 2MB (320x180px recomendado)</p>
                      </div>
                    </div>
                  </label>
                </div>
              )}
            </div>
            {errors.thumbnail && (
              <p className="text-sm text-red-600">{errors.thumbnail}</p>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="files" className="space-y-4">
          <div className="text-center py-8 bg-gray-50 rounded-lg border border-dashed border-gray-200">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-2">Gerenciamento de arquivos</p>
            <p className="text-sm text-gray-500 mb-4">Funcionalidade em desenvolvimento</p>
            <Button variant="outline" disabled>
              <Upload className="h-4 w-4 mr-2" />
              Adicionar Arquivos
            </Button>
          </div>
        </TabsContent>
      </Tabs>

      {/* Actions */}
      <div className="flex justify-end gap-3 pt-4 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancelar
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
          className="bg-[#36B37E] hover:bg-[#2a8f66]"
        >
          {isLoading ? 'Salvando...' : lesson ? 'Atualizar' : 'Criar Aula'}
        </Button>
      </div>
    </form>
  )
}