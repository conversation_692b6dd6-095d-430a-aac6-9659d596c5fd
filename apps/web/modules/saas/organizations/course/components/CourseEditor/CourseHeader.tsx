'use client'

import { useState } from 'react'
import { CourseHeaderProps } from '../../types'
import { Button } from '@/modules/ui/components/button'
import { Input } from '@/modules/ui/components/input'
import { Textarea } from '@/modules/ui/components/textarea'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/modules/ui/components/dialog'
import { Card, CardContent } from '@/modules/ui/components/card'
import { Edit, Upload } from 'lucide-react'
import { cn } from '@/modules/ui/lib'

export function CourseHeader({ course, onEdit, onSave }: CourseHeaderProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    name: course.name || '',
    description: course.description || '',
    shortDescription: course.shortDescription || ''
  })
  const [bannerImage, setBannerImage] = useState(course.courseBanners?.[0]?.image || '')

  const handleSave = () => {
    onSave({
      ...formData,
      courseBanners: bannerImage ? [{
        id: course.courseBanners?.[0]?.id || 0,
        title: formData.name,
        description: formData.description,
        position: 0,
        image: bannerImage
      }] : []
    })
    setIsEditing(false)
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setBannerImage(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  return (
    <Card className="overflow-hidden">
      <div 
        className={cn(
          "relative h-64 bg-gradient-to-r from-[#36B37E] to-[#2a8f66]",
          bannerImage && "bg-cover bg-center"
        )}
        style={bannerImage ? { backgroundImage: `url(${bannerImage})` } : {}}
      >
        <div className="absolute inset-0 bg-black/20" />
        
        <div className="relative h-full flex items-end p-6">
          <div className="text-white space-y-2">
            <h1 className="text-3xl font-bold">{course.name}</h1>
            {course.shortDescription && (
              <p className="text-white/90">{course.shortDescription}</p>
            )}
          </div>
          
          <Dialog open={isEditing} onOpenChange={setIsEditing}>
            <DialogTrigger asChild>
              <Button 
                className="absolute top-4 right-4 bg-[#36B37E] hover:bg-[#2a8f66]"
                onClick={() => setIsEditing(true)}
              >
                <Edit className="h-4 w-4 mr-2" />
                Editar Header
              </Button>
            </DialogTrigger>
            
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Editar Header do Curso</DialogTitle>
              </DialogHeader>
              
              <div className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Banner do Curso</label>
                    <div className="mt-2">
                      <div 
                        className={cn(
                          "relative h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center cursor-pointer hover:border-gray-400",
                          bannerImage && "border-solid"
                        )}
                        style={bannerImage ? { backgroundImage: `url(${bannerImage})`, backgroundSize: 'cover', backgroundPosition: 'center' } : {}}
                      >
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleImageUpload}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        />
                        {!bannerImage && (
                          <div className="text-center">
                            <Upload className="h-8 w-8 mx-auto text-gray-400" />
                            <p className="mt-2 text-sm text-gray-500">Clique para fazer upload</p>
                            <p className="text-xs text-gray-400">Recomendado: 1920x400px</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium">Nome da área de membros</label>
                    <Input
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Digite o nome do curso"
                      className="mt-2"
                    />
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium">Descrição curta</label>
                    <Input
                      value={formData.shortDescription}
                      onChange={(e) => setFormData(prev => ({ ...prev, shortDescription: e.target.value }))}
                      placeholder="Descrição que aparece no banner"
                      className="mt-2"
                    />
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium">Descrição completa</label>
                    <Textarea
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Descrição detalhada do curso"
                      className="mt-2"
                      rows={4}
                    />
                  </div>
                </div>
                
                <div className="flex justify-end gap-3">
                  <Button variant="outline" onClick={() => setIsEditing(false)}>
                    Cancelar
                  </Button>
                  <Button onClick={handleSave} className="bg-[#36B37E] hover:bg-[#2a8f66]">
                    Salvar
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </Card>
  )
}