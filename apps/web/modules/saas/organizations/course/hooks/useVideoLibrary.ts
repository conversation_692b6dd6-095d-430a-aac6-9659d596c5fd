'use client'

import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

// Video library types
interface VideoItem {
  id: string
  title: string
  description?: string
  thumbnail: string
  duration: string
  uploadDate: string
  size: string
  url: string
  status: 'processing' | 'ready' | 'error'
  tags?: string[]
}

interface VideoUpload {
  file: File
  progress: number
  status: 'uploading' | 'processing' | 'completed' | 'error'
  error?: string
}

interface UseVideoLibraryProps {
  organizationSlug?: string
}

interface VideoLibraryState {
  videos: VideoItem[]
  uploads: VideoUpload[]
  isLoading: boolean
  error: string | null
  searchTerm: string
  selectedTags: string[]
  sortBy: 'date' | 'title' | 'duration'
  sortOrder: 'asc' | 'desc'
}

interface VideoLibraryActions {
  // Search and filter
  setSearchTerm: (term: string) => void
  setSelectedTags: (tags: string[]) => void
  setSortBy: (sortBy: 'date' | 'title' | 'duration') => void
  setSortOrder: (order: 'asc' | 'desc') => void
  
  // Video management
  uploadVideo: (file: File, metadata?: Partial<VideoItem>) => Promise<void>
  deleteVideo: (videoId: string) => Promise<void>
  updateVideo: (videoId: string, data: Partial<VideoItem>) => Promise<void>
  
  // Utility
  refreshVideos: () => Promise<void>
  clearError: () => void
  getFilteredVideos: () => VideoItem[]
}

// Mock API functions - replace with actual Bunny.net integration
class VideoLibraryAPI {
  private baseUrl = process.env.NEXT_PUBLIC_BUNNY_API_URL || 'https://api.bunny.net'
  private apiKey = process.env.NEXT_PUBLIC_BUNNY_API_KEY || ''

  async getVideos(organizationSlug: string): Promise<VideoItem[]> {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            id: '1',
            title: 'Introdução ao Curso',
            description: 'Vídeo de introdução explicando os objetivos do curso',
            thumbnail: 'https://via.placeholder.com/320x180',
            duration: '15:30',
            uploadDate: '2024-01-15T10:00:00Z',
            size: '125 MB',
            url: 'https://video.bunnycdn.com/play/123/abc',
            status: 'ready',
            tags: ['introdução', 'curso']
          },
          {
            id: '2',
            title: 'Configuração do Ambiente',
            description: 'Como configurar o ambiente de desenvolvimento',
            thumbnail: 'https://via.placeholder.com/320x180',
            duration: '22:45',
            uploadDate: '2024-01-14T14:30:00Z',
            size: '180 MB',
            url: 'https://video.bunnycdn.com/play/123/def',
            status: 'ready',
            tags: ['configuração', 'ambiente']
          },
          {
            id: '3',
            title: 'Primeiros Passos',
            description: 'Primeiros passos no desenvolvimento',
            thumbnail: 'https://via.placeholder.com/320x180',
            duration: '18:20',
            uploadDate: '2024-01-13T09:15:00Z',
            size: '150 MB',
            url: 'https://video.bunnycdn.com/play/123/ghi',
            status: 'processing',
            tags: ['tutorial', 'básico']
          }
        ])
      }, 1000)
    })
  }

  async uploadVideo(file: File, metadata: Partial<VideoItem> = {}): Promise<VideoItem> {
    // Mock implementation - replace with actual Bunny.net upload
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() > 0.1) { // 90% success rate
          resolve({
            id: Date.now().toString(),
            title: metadata.title || file.name.replace(/\.[^/.]+$/, ''),
            description: metadata.description || '',
            thumbnail: 'https://via.placeholder.com/320x180',
            duration: '00:00',
            uploadDate: new Date().toISOString(),
            size: `${Math.round(file.size / 1024 / 1024)} MB`,
            url: `https://video.bunnycdn.com/play/123/${Date.now()}`,
            status: 'processing',
            tags: metadata.tags || []
          })
        } else {
          reject(new Error('Falha no upload do vídeo'))
        }
      }, 2000)
    })
  }

  async deleteVideo(videoId: string): Promise<void> {
    // Mock implementation
    return new Promise((resolve) => {
      setTimeout(resolve, 500)
    })
  }

  async updateVideo(videoId: string, data: Partial<VideoItem>): Promise<VideoItem> {
    // Mock implementation
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id: videoId,
          title: 'Updated Video',
          thumbnail: 'https://via.placeholder.com/320x180',
          duration: '10:00',
          uploadDate: new Date().toISOString(),
          size: '100 MB',
          url: 'https://video.bunnycdn.com/play/123/updated',
          status: 'ready',
          ...data
        })
      }, 500)
    })
  }
}

export function useVideoLibrary({ 
  organizationSlug 
}: UseVideoLibraryProps = {}): VideoLibraryState & VideoLibraryActions {
  const queryClient = useQueryClient()
  const [uploads, setUploads] = useState<VideoUpload[]>([])
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [sortBy, setSortBy] = useState<'date' | 'title' | 'duration'>('date')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  
  const videoAPI = new VideoLibraryAPI()

  // Fetch videos
  const { 
    data: videos = [], 
    isLoading, 
    error: queryError,
    refetch 
  } = useQuery({
    queryKey: ['videos', organizationSlug],
    queryFn: () => videoAPI.getVideos(organizationSlug || ''),
    enabled: !!organizationSlug,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Upload video mutation
  const uploadMutation = useMutation({
    mutationFn: ({ file, metadata }: { file: File; metadata?: Partial<VideoItem> }) => 
      videoAPI.uploadVideo(file, metadata),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['videos', organizationSlug] })
    },
    onError: (error: any) => {
      setError(error.message || 'Erro ao fazer upload do vídeo')
    }
  })

  // Delete video mutation
  const deleteMutation = useMutation({
    mutationFn: (videoId: string) => videoAPI.deleteVideo(videoId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['videos', organizationSlug] })
    },
    onError: (error: any) => {
      setError(error.message || 'Erro ao excluir vídeo')
    }
  })

  // Update video mutation
  const updateMutation = useMutation({
    mutationFn: ({ videoId, data }: { videoId: string; data: Partial<VideoItem> }) => 
      videoAPI.updateVideo(videoId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['videos', organizationSlug] })
    },
    onError: (error: any) => {
      setError(error.message || 'Erro ao atualizar vídeo')
    }
  })

  // Set error from query
  useEffect(() => {
    if (queryError) {
      setError(queryError.message || 'Erro ao carregar vídeos')
    }
  }, [queryError])

  // Actions
  const uploadVideo = async (file: File, metadata?: Partial<VideoItem>) => {
    const uploadId = Date.now().toString()
    
    // Add to uploads list
    const newUpload: VideoUpload = {
      file,
      progress: 0,
      status: 'uploading'
    }
    setUploads(prev => [...prev, newUpload])

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploads(prev => prev.map(upload => 
          upload.file === file && upload.progress < 90
            ? { ...upload, progress: upload.progress + 10 }
            : upload
        ))
      }, 200)

      await uploadMutation.mutateAsync({ file, metadata })
      
      clearInterval(progressInterval)
      
      // Update upload status
      setUploads(prev => prev.map(upload => 
        upload.file === file
          ? { ...upload, progress: 100, status: 'completed' }
          : upload
      ))
      
      // Remove from uploads after 3 seconds
      setTimeout(() => {
        setUploads(prev => prev.filter(upload => upload.file !== file))
      }, 3000)
      
    } catch (error: any) {
      setUploads(prev => prev.map(upload => 
        upload.file === file
          ? { ...upload, status: 'error', error: error.message }
          : upload
      ))
    }
  }

  const deleteVideo = async (videoId: string) => {
    if (window.confirm('Tem certeza que deseja excluir este vídeo? Esta ação não pode ser desfeita.')) {
      await deleteMutation.mutateAsync(videoId)
    }
  }

  const updateVideo = async (videoId: string, data: Partial<VideoItem>) => {
    await updateMutation.mutateAsync({ videoId, data })
  }

  const refreshVideos = async () => {
    await refetch()
  }

  const clearError = () => {
    setError(null)
  }

  const getFilteredVideos = (): VideoItem[] => {
    let filtered = [...videos]

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(video => 
        video.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        video.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Apply tag filter
    if (selectedTags.length > 0) {
      filtered = filtered.filter(video => 
        video.tags?.some(tag => selectedTags.includes(tag))
      )
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0
      
      switch (sortBy) {
        case 'title':
          comparison = a.title.localeCompare(b.title)
          break
        case 'date':
          comparison = new Date(a.uploadDate).getTime() - new Date(b.uploadDate).getTime()
          break
        case 'duration':
          // Convert duration to seconds for comparison
          const aDuration = durationToSeconds(a.duration)
          const bDuration = durationToSeconds(b.duration)
          comparison = aDuration - bDuration
          break
      }
      
      return sortOrder === 'asc' ? comparison : -comparison
    })

    return filtered
  }

  const durationToSeconds = (duration: string): number => {
    const parts = duration.split(':').map(Number)
    if (parts.length === 2) {
      return parts[0] * 60 + parts[1] // MM:SS
    } else if (parts.length === 3) {
      return parts[0] * 3600 + parts[1] * 60 + parts[2] // HH:MM:SS
    }
    return 0
  }

  return {
    // State
    videos,
    uploads,
    isLoading,
    error,
    searchTerm,
    selectedTags,
    sortBy,
    sortOrder,
    
    // Actions
    setSearchTerm,
    setSelectedTags,
    setSortBy,
    setSortOrder,
    uploadVideo,
    deleteVideo,
    updateVideo,
    refreshVideos,
    clearError,
    getFilteredVideos
  }
}