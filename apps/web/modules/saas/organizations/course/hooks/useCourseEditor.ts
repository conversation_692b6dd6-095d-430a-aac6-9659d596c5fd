'use client'

import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Course, Module, Lesson } from '../types'
import { CourseAPI } from '../lib/api'

interface UseCourseEditorProps {
  courseId?: string
  organizationSlug?: string
}

interface CourseEditorState {
  course: Course | null
  modules: Module[]
  isLoading: boolean
  isSaving: boolean
  error: string | null
}

interface CourseEditorActions {
  // Course actions
  updateCourse: (data: Partial<Course>) => Promise<void>
  saveCourse: () => Promise<void>
  
  // Module actions
  addModule: (data: Partial<Module>) => Promise<void>
  updateModule: (moduleId: string, data: Partial<Module>) => Promise<void>
  deleteModule: (moduleId: string) => Promise<void>
  reorderModules: (modules: Module[]) => Promise<void>
  
  // Lesson actions
  addLesson: (moduleId: string, data?: Partial<Lesson>) => Promise<void>
  updateLesson: (lessonId: string, data: Partial<Lesson>) => Promise<void>
  deleteLesson: (lessonId: string) => Promise<void>
  reorderLessons: (lessons: Lesson[]) => Promise<void>
  
  // Utility actions
  refreshData: () => Promise<void>
  clearError: () => void
}

export function useCourseEditor({ 
  courseId, 
  organizationSlug 
}: UseCourseEditorProps): CourseEditorState & CourseEditorActions {
  const queryClient = useQueryClient()
  const [error, setError] = useState<string | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  
  const courseAPI = new CourseAPI()

  // Fetch course data
  const { 
    data: courseData, 
    isLoading, 
    error: queryError,
    refetch 
  } = useQuery({
    queryKey: ['course', courseId],
    queryFn: () => courseAPI.getCourse(courseId!),
    enabled: !!courseId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  const course = courseData?.course || null
  const modules = courseData?.modules || []

  // Update course mutation
  const updateCourseMutation = useMutation({
    mutationFn: (data: Partial<Course>) => 
      courseAPI.updateCourse(courseId!, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['course', courseId] })
    },
    onError: (error: any) => {
      setError(error.message || 'Erro ao atualizar curso')
    }
  })

  // Add module mutation
  const addModuleMutation = useMutation({
    mutationFn: (data: Partial<Module>) => 
      courseAPI.createModule(courseId!, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['course', courseId] })
    },
    onError: (error: any) => {
      setError(error.message || 'Erro ao criar módulo')
    }
  })

  // Update module mutation
  const updateModuleMutation = useMutation({
    mutationFn: ({ moduleId, data }: { moduleId: string; data: Partial<Module> }) => 
      courseAPI.updateModule(moduleId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['course', courseId] })
    },
    onError: (error: any) => {
      setError(error.message || 'Erro ao atualizar módulo')
    }
  })

  // Delete module mutation
  const deleteModuleMutation = useMutation({
    mutationFn: (moduleId: string) => 
      courseAPI.deleteModule(moduleId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['course', courseId] })
    },
    onError: (error: any) => {
      setError(error.message || 'Erro ao excluir módulo')
    }
  })

  // Add lesson mutation
  const addLessonMutation = useMutation({
    mutationFn: ({ moduleId, data }: { moduleId: string; data: Partial<Lesson> }) => 
      courseAPI.createLesson(moduleId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['course', courseId] })
    },
    onError: (error: any) => {
      setError(error.message || 'Erro ao criar aula')
    }
  })

  // Update lesson mutation
  const updateLessonMutation = useMutation({
    mutationFn: ({ lessonId, data }: { lessonId: string; data: Partial<Lesson> }) => 
      courseAPI.updateLesson(lessonId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['course', courseId] })
    },
    onError: (error: any) => {
      setError(error.message || 'Erro ao atualizar aula')
    }
  })

  // Delete lesson mutation
  const deleteLessonMutation = useMutation({
    mutationFn: (lessonId: string) => 
      courseAPI.deleteLesson(lessonId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['course', courseId] })
    },
    onError: (error: any) => {
      setError(error.message || 'Erro ao excluir aula')
    }
  })

  // Reorder modules mutation
  const reorderModulesMutation = useMutation({
    mutationFn: (modules: Module[]) => 
      courseAPI.reorderModules(courseId!, modules),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['course', courseId] })
    },
    onError: (error: any) => {
      setError(error.message || 'Erro ao reordenar módulos')
    }
  })

  // Reorder lessons mutation
  const reorderLessonsMutation = useMutation({
    mutationFn: (lessons: Lesson[]) => 
      courseAPI.reorderLessons(lessons),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['course', courseId] })
    },
    onError: (error: any) => {
      setError(error.message || 'Erro ao reordenar aulas')
    }
  })

  // Set error from query
  useEffect(() => {
    if (queryError) {
      setError(queryError.message || 'Erro ao carregar curso')
    }
  }, [queryError])

  // Actions
  const updateCourse = async (data: Partial<Course>) => {
    setIsSaving(true)
    try {
      await updateCourseMutation.mutateAsync(data)
    } finally {
      setIsSaving(false)
    }
  }

  const saveCourse = async () => {
    if (!course) return
    await updateCourse(course)
  }

  const addModule = async (data: Partial<Module>) => {
    const moduleData = {
      name: data.name || 'Novo Módulo',
      description: data.description || '',
      position: modules.length + 1,
      ...data
    }
    await addModuleMutation.mutateAsync(moduleData)
  }

  const updateModule = async (moduleId: string, data: Partial<Module>) => {
    await updateModuleMutation.mutateAsync({ moduleId, data })
  }

  const deleteModule = async (moduleId: string) => {
    if (window.confirm('Tem certeza que deseja excluir este módulo? Esta ação não pode ser desfeita.')) {
      await deleteModuleMutation.mutateAsync(moduleId)
    }
  }

  const reorderModules = async (reorderedModules: Module[]) => {
    await reorderModulesMutation.mutateAsync(reorderedModules)
  }

  const addLesson = async (moduleId: string, data: Partial<Lesson> = {}) => {
    const module = modules.find(m => m.id === moduleId)
    const lessonData = {
      name: data.name || 'Nova Aula',
      description: data.description || '',
      position: (module?.lessons?.length || 0) + 1,
      moduleId,
      status: 'draft' as const,
      ...data
    }
    await addLessonMutation.mutateAsync({ moduleId, data: lessonData })
  }

  const updateLesson = async (lessonId: string, data: Partial<Lesson>) => {
    await updateLessonMutation.mutateAsync({ lessonId, data })
  }

  const deleteLesson = async (lessonId: string) => {
    if (window.confirm('Tem certeza que deseja excluir esta aula? Esta ação não pode ser desfeita.')) {
      await deleteLessonMutation.mutateAsync(lessonId)
    }
  }

  const reorderLessons = async (reorderedLessons: Lesson[]) => {
    await reorderLessonsMutation.mutateAsync(reorderedLessons)
  }

  const refreshData = async () => {
    await refetch()
  }

  const clearError = () => {
    setError(null)
  }

  const isAnyMutationLoading = 
    updateCourseMutation.isPending ||
    addModuleMutation.isPending ||
    updateModuleMutation.isPending ||
    deleteModuleMutation.isPending ||
    addLessonMutation.isPending ||
    updateLessonMutation.isPending ||
    deleteLessonMutation.isPending ||
    reorderModulesMutation.isPending ||
    reorderLessonsMutation.isPending

  return {
    // State
    course,
    modules,
    isLoading,
    isSaving: isSaving || isAnyMutationLoading,
    error,
    
    // Actions
    updateCourse,
    saveCourse,
    addModule,
    updateModule,
    deleteModule,
    reorderModules,
    addLesson,
    updateLesson,
    deleteLesson,
    reorderLessons,
    refreshData,
    clearError
  }
}