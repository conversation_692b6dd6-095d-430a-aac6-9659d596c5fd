"use client";

import { useQuery } from "@tanstack/react-query";
import { apiClient } from "@shared/lib/api-client";
import { useSession } from "@saas/auth/hooks/use-session";

interface OrganizationStats {
	id: string;
	name: string;
	slug: string;
	logo?: string | null;
	createdAt: string;
	stats: {
		totalMembers: number;
		activeMembers: number;
		totalCourses: number;
		totalEnrollments: number;
		totalVitrines: number;
		publishedVitrines: number;
		totalViews: number;
		daysSinceLastActivity: number | null;
	};
}

export function useOrganizationStats() {
	const { user } = useSession();

	return useQuery({
		queryKey: ["organization-stats", user?.id],
		queryFn: async (): Promise<OrganizationStats[]> => {
			if (!user?.id) {
				throw new Error("User not authenticated");
			}

			const response = await apiClient.organizations.stats.$get({
				query: {
					userId: user.id,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to fetch organization stats");
			}

			return response.json();
		},
		enabled: !!user?.id,
	});
}
