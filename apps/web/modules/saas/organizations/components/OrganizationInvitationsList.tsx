"use client";

import type { ActiveOrganization } from "@repo/auth";
import { authClient } from "@repo/auth/client";
import { isOrganizationAdmin } from "@repo/auth/lib/helper";
import { useSession } from "@saas/auth/hooks/use-session";
import {
	fullOrganizationQueryKey,
	useFullOrganizationQuery,
} from "@saas/organizations/lib/api";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	CheckIcon,
	ClockIcon,
	MailXIcon,
	MoreVerticalIcon,
	XIcon,
	MailIcon,
	UserPlusIcon,
	AlertCircleIcon
} from "lucide-react";
import { useFormatter, useTranslations } from "next-intl";
import { useMemo } from "react";
import { OrganizationRoleSelect } from "./OrganizationRoleSelect";
import { toast } from "sonner";

export function OrganizationInvitationsList({
	organizationId,
}: {
	organizationId: string;
}) {
	const t = useTranslations();
	const queryClient = useQueryClient();
	const { user } = useSession();
	const formatter = useFormatter();
	const { data: organization } = useFullOrganizationQuery(organizationId);

	const canUserEditInvitations = isOrganizationAdmin(organization, user);

	const invitations = useMemo(
		() =>
			organization?.invitations
				?.filter((invitation) => invitation.status === "pending")
				.sort(
					(a, b) =>
						new Date(a.expiresAt).getTime() -
						new Date(b.expiresAt).getTime(),
				),
		[organization?.invitations],
	);

	const revokeInvitation = (invitationId: string) => {
		toast.promise(
			async () => {
				const { error } =
					await authClient.organization.cancelInvitation({
						invitationId,
					});

				if (error) {
					throw error;
				}
			},
			{
				loading: t(
					"organizations.settings.members.notifications.revokeInvitation.loading.description",
				),
				success: () => {
					queryClient.invalidateQueries({
						queryKey: fullOrganizationQueryKey(organizationId),
					});
					return t(
						"organizations.settings.members.notifications.revokeInvitation.success.description",
					);
				},
				error: t(
					"organizations.settings.members.notifications.revokeInvitation.error.description",
				),
			},
		);
	};

	const getStatusBadge = (status: string) => {
		switch (status) {
			case "pending":
				return (
					<Badge status="warning" className="text-xs flex items-center">
						<ClockIcon className="h-3 w-3 mr-1" />
						Pendente
					</Badge>
				);
			case "accepted":
				return (
					<Badge status="success" className="text-xs flex items-center">
						<CheckIcon className="h-3 w-3 mr-1" />
						Aceito
					</Badge>
				);
			case "rejected":
			case "canceled":
				return (
					<Badge status="error" className="text-xs flex items-center">
						<XIcon className="h-3 w-3 mr-1" />
						Cancelado
					</Badge>
				);
			default:
				return (
					<Badge status="muted" className="text-xs">
						{status}
					</Badge>
				);
		}
	};

	const getRoleBadge = (role: string) => {
		switch (role) {
			case "owner":
				return (
					<Badge status="warning" className="text-xs flex items-center">
						<UserPlusIcon className="h-3 w-3 mr-1" />
						Proprietário
					</Badge>
				);
			case "admin":
				return (
					<Badge status="success" className="text-xs flex items-center">
						<UserPlusIcon className="h-3 w-3 mr-1" />
						Administrador
					</Badge>
				);
			case "member":
				return (
					<Badge status="info" className="text-xs flex items-center">
						<UserPlusIcon className="h-3 w-3 mr-1" />
						Membro
					</Badge>
				);
			default:
				return (
					<Badge status="muted" className="text-xs">
						{role}
					</Badge>
				);
		}
	};

	const formatExpiryDate = (date: string) => {
		const expiryDate = new Date(date);
		const now = new Date();
		const diffTime = expiryDate.getTime() - now.getTime();
		const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

		if (diffDays < 0) {
			return "Expirado";
		} else if (diffDays === 0) {
			return "Expira hoje";
		} else if (diffDays === 1) {
			return "Expira amanhã";
		} else {
			return `Expira em ${diffDays} dias`;
		}
	};

	return (
		<div className="space-y-4">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h3 className="text-lg font-semibold">Convites Pendentes</h3>
					<p className="text-sm text-muted-foreground">
						{invitations?.length || 0} convite{invitations?.length !== 1 ? 's' : ''} aguardando resposta
					</p>
				</div>
			</div>

			{/* Invitations Grid */}
			{invitations?.length === 0 ? (
				<div className="h-32 flex flex-col items-center justify-center text-muted-foreground">
					<AlertCircleIcon className="h-8 w-8 mb-2 opacity-50" />
					<p>Nenhum convite pendente encontrado.</p>
					<p className="text-sm">Todos os convites foram aceitos ou expiraram.</p>
				</div>
			) : (
				<div className="grid gap-4 grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
					{invitations?.map((invitation) => (
						<Card key={invitation.id} className="group hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-orange/5 to-orange/10 border-orange/20">
							<CardHeader className="pb-3">
								<div className="flex items-start justify-between">
									<div className="flex-1 min-w-0">
										<CardTitle className="text-base font-semibold text-foreground truncate">
											{invitation.email}
										</CardTitle>
										<div className="flex gap-1.5 mt-1.5">
											{getStatusBadge(invitation.status)}
											{getRoleBadge(invitation.role || "member")}
										</div>
									</div>
									{canUserEditInvitations && (
										<DropdownMenu>
											<DropdownMenuTrigger asChild>
												<Button
													variant="ghost"
													size="sm"
													className="h-8 w-8 p-0"
												>
													<MoreVerticalIcon className="h-4 w-4" />
												</Button>
											</DropdownMenuTrigger>
											<DropdownMenuContent align="end">
												<DropdownMenuItem
													disabled={invitation.status !== "pending"}
													onClick={() => revokeInvitation(invitation.id)}
													className="text-destructive"
												>
													<MailXIcon className="mr-2 size-4" />
													{t(
														"organizations.settings.members.invitations.revoke",
													)}
												</DropdownMenuItem>
											</DropdownMenuContent>
										</DropdownMenu>
									)}
								</div>
							</CardHeader>

							<CardContent className="pt-0">
								<div className="space-y-2">
									{/* Invitation Details - More compact */}
									<div className="grid grid-cols-1 gap-2 text-xs">
										<div className="flex items-center gap-2">
											<ClockIcon className="h-3 w-3 text-muted-foreground" />
											<div>
												<p className="text-muted-foreground">Expira em</p>
												<p className="font-medium">{formatExpiryDate(invitation.expiresAt)}</p>
											</div>
										</div>
										<div className="flex items-center gap-2">
											<MailIcon className="h-3 w-3 text-muted-foreground" />
											<div>
												<p className="text-muted-foreground">Enviado em</p>
												<p className="font-medium">
													{typeof invitation.expiresAt === 'string'
														? new Date(invitation.expiresAt).toLocaleDateString('pt-BR')
														: invitation.expiresAt.toLocaleDateString('pt-BR')
													}
												</p>
											</div>
										</div>
									</div>

									{/* Role Management - More compact */}
									{canUserEditInvitations && invitation.status === "pending" && (
										<div className="flex gap-2 pt-2 border-t">
											<OrganizationRoleSelect
												value={invitation.role || "member"}
												disabled
												onSelect={() => {
													return;
												}}
											/>
										</div>
									)}
								</div>
							</CardContent>
						</Card>
					))}
				</div>
			)}
		</div>
	);
}
