"use client";


import { useState } from "react";
import { useActiveOrganization } from "../hooks/use-active-organization";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/modules/ui/components/card";
import { Input } from "@/modules/ui/components/input";
import { Button } from "@/modules/ui/components/button";

export function OrganizationDomainForm() {
  const { activeOrganization } = useActiveOrganization();
  const [subdomain, setSubdomain] = useState(activeOrganization?.subdomain || "");
  const [customDomain, setCustomDomain] = useState(activeOrganization?.customDomain || "");

  const handleSubdomainSave = async () => {
    // API call to update subdomain
  };

  const handleCustomDomainSave = async () => {
    // API call to update custom domain
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Subdomínio</CardTitle>
          <CardDescription>
            Configure um subdomínio para sua organização (ex: minhaorg.cakto.com.br)
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Input
              value={subdomain}
              onChange={(e) => setSubdomain(e.target.value)}
              placeholder="minhaorg"
            />
            <span className="text-muted-foreground">.cakto.com.br</span>
          </div>
          <Button onClick={handleSubdomainSave}>Salvar Subdomínio</Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Domínio Personalizado</CardTitle>
          <CardDescription>
            Configure um domínio personalizado para sua organização
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Input
            value={customDomain}
            onChange={(e) => setCustomDomain(e.target.value)}
            placeholder="members.minhaempresa.com"
          />
          <Button onClick={handleCustomDomainSave}>Salvar Domínio</Button>
        </CardContent>
      </Card>
    </div>
  );
}
