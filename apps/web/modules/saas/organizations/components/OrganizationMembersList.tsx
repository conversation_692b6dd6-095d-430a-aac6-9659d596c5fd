"use client";
import type { OrganizationMemberRole } from "@repo/auth";
import { authClient } from "@repo/auth/client";
import { isOrganizationAdmin } from "@repo/auth/lib/helper";
import { useSession } from "@saas/auth/hooks/use-session";
import { useOrganizationMemberRoles } from "@saas/organizations/hooks/member-roles";
import {
	fullOrganizationQueryKey,
	useFullOrganizationQuery,
} from "@saas/organizations/lib/api";
import { UserAvatar } from "@shared/components/UserAvatar";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Input } from "@ui/components/input";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import {
	LogOutIcon,
	MoreVerticalIcon,
	TrashIcon,
	Search,
	Filter,
	ChevronLeft,
	ChevronRight,
	ChevronsLeft,
	ChevronsRight,
	MailIcon,
	UserPlusIcon,
	UsersIcon,
	ShieldIcon,
	User2,
	ClockIcon,
	BookOpenIcon,
	ActivityIcon,
	Building2Icon,
	PlusIcon
} from "lucide-react";
import { useTranslations } from "next-intl";
import { useState, useMemo } from "react";
import { toast } from "sonner";
import { parseAsInteger, parseAsString, useQueryState } from "nuqs";
import { OrganizationRoleSelect } from "./OrganizationRoleSelect";
import { InviteMemberModal } from "./InviteMemberModal";

const ITEMS_PER_PAGE = 12;

interface MembersPaginationProps {
	pagination: {
		total: number;
		pages: number;
		limit: number;
	};
	currentPage: number;
	onPageChange: (page: number) => void;
}

function MembersPaginationComponent({
	pagination,
	currentPage,
	onPageChange
}: MembersPaginationProps) {
	const { total, pages, limit } = pagination;

	const startItem = (currentPage - 1) * limit + 1;
	const endItem = Math.min(currentPage * limit, total);

	const canGoPrevious = currentPage > 1;
	const canGoNext = currentPage < pages;

	const getVisiblePages = () => {
		const delta = 2;
		const range = [];
		const rangeWithDots = [];

		for (
			let i = Math.max(2, currentPage - delta);
			i <= Math.min(pages - 1, currentPage + delta);
			i++
		) {
			range.push(i);
		}

		if (currentPage - delta > 2) {
			rangeWithDots.push(1, '...');
		} else {
			rangeWithDots.push(1);
		}

		rangeWithDots.push(...range);

		if (currentPage + delta < pages - 1) {
			rangeWithDots.push('...', pages);
		} else {
			if (pages > 1) {
				rangeWithDots.push(pages);
			}
		}

		return rangeWithDots;
	};

	if (pages <= 1) {
		return null;
	}

	return (
		<div className="flex items-center justify-between px-2">
			<div className="flex-1 text-sm text-muted-foreground">
				Mostrando {startItem} a {endItem} de {total} membros
			</div>

			<div className="flex items-center space-x-2">
				{/* First page */}
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(1)}
					disabled={!canGoPrevious}
					className="hidden sm:flex"
				>
					<ChevronsLeft className="h-4 w-4" />
				</Button>

				{/* Previous page */}
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(currentPage - 1)}
					disabled={!canGoPrevious}
				>
					<ChevronLeft className="h-4 w-4" />
					<span className="hidden sm:inline ml-1">Anterior</span>
				</Button>

				{/* Page numbers */}
				<div className="hidden md:flex items-center space-x-1">
					{getVisiblePages().map((page, index) => (
						<Button
							key={index}
							variant={page === currentPage ? "primary" : "outline"}
							size="sm"
							onClick={() => typeof page === 'number' && onPageChange(page)}
							disabled={page === '...'}
							className="min-w-[2.5rem]"
						>
							{page}
						</Button>
					))}
				</div>

				{/* Current page indicator for mobile */}
				<div className="md:hidden text-sm text-muted-foreground">
					{currentPage} / {pages}
				</div>

				{/* Next page */}
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(currentPage + 1)}
					disabled={!canGoNext}
				>
					<span className="hidden sm:inline mr-1">Próxima</span>
					<ChevronRight className="h-4 w-4" />
				</Button>

				{/* Last page */}
				<Button
					variant="outline"
					size="sm"
					onClick={() => onPageChange(pages)}
					disabled={!canGoNext}
					className="hidden sm:flex"
				>
					<ChevronsRight className="h-4 w-4" />
				</Button>
			</div>
		</div>
	);
}

export function OrganizationMembersList({
	organizationId,
}: {
	organizationId: string;
}) {
	const t = useTranslations();
	const queryClient = useQueryClient();
	const { user } = useSession();
	const { data: organization } = useFullOrganizationQuery(organizationId);
	const memberRoles = useOrganizationMemberRoles();

	const [currentPage, setCurrentPage] = useQueryState(
		"currentPage",
		parseAsInteger.withDefault(1),
	);
	const [searchTerm, setSearchTerm] = useQueryState(
		"query",
		parseAsString.withDefault(""),
	);
	const [roleFilter, setRoleFilter] = useQueryState(
		"role",
		parseAsString.withDefault("all"),
	);

	const userIsOrganizationAdmin = isOrganizationAdmin(organization, user);

	const updateMemberRole = async (
		memberId: string,
		role: OrganizationMemberRole,
	) => {
		toast.promise(
			async () => {
				await authClient.organization.updateMemberRole({
					memberId,
					role,
					organizationId,
				});
			},
			{
				loading: t(
					"organizations.settings.members.notifications.updateMembership.loading.description",
				),
				success: () => {
					queryClient.invalidateQueries({
						queryKey: fullOrganizationQueryKey(organizationId),
					});

					return t(
						"organizations.settings.members.notifications.updateMembership.success.description",
					);
				},
				error: t(
					"organizations.settings.members.notifications.updateMembership.error.description",
				),
			},
		);
	};

	const removeMember = async (memberId: string) => {
		toast.promise(
			async () => {
				await authClient.organization.removeMember({
					memberIdOrEmail: memberId,
					organizationId,
				});
			},
			{
				loading: t(
					"organizations.settings.members.notifications.removeMember.loading.description",
				),
				success: () => {
					queryClient.invalidateQueries({
						queryKey: fullOrganizationQueryKey(organizationId),
					});

					return t(
						"organizations.settings.members.notifications.removeMember.success.description",
					);
				},
				error: t(
					"organizations.settings.members.notifications.removeMember.error.description",
				),
			},
		);
	};

	const members = useMemo(() => {
		let filteredMembers = organization?.members ?? [];

		// Filter by search term
		if (searchTerm) {
			filteredMembers = filteredMembers.filter(member =>
				member.user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
				member.user.email.toLowerCase().includes(searchTerm.toLowerCase())
			);
		}

		// Filter by role
		if (roleFilter !== "all") {
			filteredMembers = filteredMembers.filter(member => member.role === roleFilter);
		}

		return filteredMembers;
	}, [organization?.members, searchTerm, roleFilter]);

	const pagination = useMemo(() => {
		const total = members.length;
		const pages = Math.ceil(total / ITEMS_PER_PAGE);
		return { total, pages, limit: ITEMS_PER_PAGE };
	}, [members.length]);

	const paginatedMembers = useMemo(() => {
		const start = (currentPage - 1) * ITEMS_PER_PAGE;
		const end = start + ITEMS_PER_PAGE;
		return members.slice(start, end);
	}, [members, currentPage]);

	const clearFilters = () => {
		setSearchTerm("");
		setRoleFilter("all");
		setCurrentPage(1);
	};

	const hasActiveFilters = searchTerm || roleFilter !== "all";

	const getStatusFromLastActive = (member: any) => {
		// Mock logic - in real implementation, this would be based on actual last activity
		const randomActive = Math.random() > 0.3;
		return randomActive ? "Ativo" : "Inativo";
	};

	const getMemberStats = (member: any) => {
		// Mock stats - in real implementation, fetch from API
		return {
			coursesCompleted: Math.floor(Math.random() * 5),
			coursesEnrolled: Math.floor(Math.random() * 8) + 1,
			lastActive: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR')
		};
	};

	// Get workspace context
	const workspaceContext = organization?.name || "Organização";

	return (
		<>
			<div className="space-y-6">
				{/* Header with better hierarchy */}
				<div className="space-y-4">
									<div className="flex items-center justify-between">
					<div className="space-y-1">
						<h2 className="text-2xl font-semibold tracking-tight">Gestão de Membros</h2>
						<div className="flex items-center gap-2">
							<p className="text-sm text-muted-foreground">Gerencie membros da organização</p>
							<Badge status="info" className="text-xs flex items-center h-5">
								<Building2Icon className="h-3 w-3 mr-1" />
								{workspaceContext}
							</Badge>
						</div>
					</div>
					{userIsOrganizationAdmin && (
						<InviteMemberModal organizationId={organizationId} />
					)}
				</div>

					{/* Filters - More compact */}
					<div className="flex flex-col sm:flex-row gap-3">
						<div className="relative flex-1">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
							<Input
								type="search"
								placeholder="Buscar por nome ou email..."
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className="pl-9"
							/>
						</div>

						<div className="flex gap-2">
							<Select value={roleFilter} onValueChange={setRoleFilter}>
								<SelectTrigger className="w-[120px]">
									<Filter className="h-4 w-4 mr-2" />
									<SelectValue placeholder="Funções" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">Funções</SelectItem>
									<SelectItem value="owner">Proprietário</SelectItem>
									<SelectItem value="admin">Administrador</SelectItem>
									<SelectItem value="member">Membro</SelectItem>
								</SelectContent>
							</Select>

							{hasActiveFilters && (
								<Button variant="outline" size="sm" onClick={clearFilters}>
									Limpar
								</Button>
							)}
						</div>
					</div>
				</div>

				{paginatedMembers.length === 0 ? (
					<div className="h-32 flex flex-col items-center justify-center text-muted-foreground">
						<UsersIcon className="h-8 w-8 mb-2 opacity-50" />
						<p>Nenhum membro encontrado.</p>
						{hasActiveFilters && (
							<Button variant="link" onClick={clearFilters} className="mt-2">
								Limpar filtros
							</Button>
						)}
					</div>
				) : (
					<>
						<div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
							{paginatedMembers.map((member) => {
								const stats = getMemberStats(member);
								const status = getStatusFromLastActive(member);

								return (
									<Card key={member.id} className="group hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
										<CardHeader className="pb-3">
											<div className="flex items-start justify-between">
												<div className="flex-1 min-w-0 flex items-center gap-3">
													<UserAvatar
														name={member.user.name ?? member.user.email}
														avatarUrl={member.user.image}
														className="h-12 w-12 rounded-full"
													/>
													<div className="min-w-0 flex-1">
														<CardTitle className="text-base font-semibold text-foreground truncate">
															{member.user.name ?? member.user.email}
														</CardTitle>
														<p className="text-xs text-muted-foreground mt-0.5 truncate">
															{member.user.email}
														</p>
														<div className="flex mt-1.5">
															{member.role === "owner" ? (
																<Badge status="warning" className="text-xs flex items-center px-1.5 py-0.5">
																	<ShieldIcon className="h-3 w-3 mr-1" /> Proprietário
																</Badge>
															) : member.role === "admin" ? (
																<Badge status="success" className="text-xs flex items-center px-1.5 py-0.5">
																	<ShieldIcon className="h-3 w-3 mr-1" /> Admin
																</Badge>
															) : (
																<Badge status="info" className="text-xs flex items-center px-1.5 py-0.5">
																	<User2 className="h-3 w-3 mr-1" /> Membro
																</Badge>
															)}
														</div>
													</div>
												</div>
												<DropdownMenu>
													<DropdownMenuTrigger asChild>
														<Button
															variant="ghost"
															size="sm"
															className="h-8 w-8 p-0"
														>
															<MoreVerticalIcon className="h-4 w-4" />
														</Button>
													</DropdownMenuTrigger>
													<DropdownMenuContent align="end">
														{member.userId !== user?.id && (
															<DropdownMenuItem
																disabled={
																	!isOrganizationAdmin(
																		organization,
																		user,
																	)
																}
																className="text-destructive"
																onClick={async () =>
																	removeMember(
																		member.id,
																	)
																}
															>
																<TrashIcon className="mr-2 size-4" />
																{t(
																	"organizations.settings.members.removeMember",
																)}
															</DropdownMenuItem>
														)}
														{member.userId === user?.id && (
															<DropdownMenuItem
																className="text-destructive"
																onClick={async () =>
																	removeMember(
																		member.id,
																	)
																}
															>
																<LogOutIcon className="mr-2 size-4" />
																{t(
																	"organizations.settings.members.leaveOrganization",
																)}
															</DropdownMenuItem>
														)}
													</DropdownMenuContent>
												</DropdownMenu>
											</div>
										</CardHeader>

										<CardContent className="pt-0">

										</CardContent>
									</Card>
								);
							})}
						</div>

						<MembersPaginationComponent
							pagination={pagination}
							currentPage={currentPage}
							onPageChange={setCurrentPage}
						/>
					</>
				)}
			</div>
		</>
	);
}
