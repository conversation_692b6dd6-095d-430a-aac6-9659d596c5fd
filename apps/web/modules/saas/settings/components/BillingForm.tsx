"use client";

import { useSession } from "@saas/auth/hooks/use-session";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
	CreditCardIcon,
	CalendarIcon,
	DollarSignIcon,
	PackageIcon,
	FileTextIcon,
	MapPinIcon,
	MailIcon,
	EyeIcon,
	XIcon,
	ReceiptIcon,
	CreditCard,
	Loader2
} from "lucide-react";
import { toast } from "sonner";
import {
	usePurchases,
	useUserPurchases,
	useCancelSubscription,
	useGetInvoice,
	usePurchaseStats
} from "@saas/payments/hooks/purchases";
import type { Purchase, UserPurchase } from "@saas/payments/types";

// Componente de loading para os itens de compra
function PurchaseItemSkeleton() {
	return (
		<div className="flex items-center justify-between p-6 border border-border rounded-xl animate-pulse">
			<div className="flex items-center gap-4">
				<div className="w-12 h-12 bg-muted rounded-full"></div>
				<div className="space-y-2">
					<div className="h-4 bg-muted rounded w-32"></div>
					<div className="h-3 bg-muted rounded w-48"></div>
					<div className="h-3 bg-muted rounded w-36"></div>
				</div>
			</div>
			<div className="flex gap-2">
				<div className="h-8 bg-muted rounded w-20"></div>
				<div className="h-8 bg-muted rounded w-20"></div>
			</div>
		</div>
	);
}

export function BillingForm() {
	const { user } = useSession();

	// Hooks para buscar dados
	const { data: purchases = [], isLoading: isLoadingPurchases } = usePurchases(user?.id);
	const { data: userPurchases = [], isLoading: isLoadingUserPurchases } = useUserPurchases(user?.id);
	const { activeSubscriptions, totalPurchases, totalRevenue, nextPaymentDate } = usePurchaseStats(user?.id);

	// Mutations
	const { mutate: cancelSubscription, isPending: isCancelling } = useCancelSubscription();
	const { mutate: getInvoice, isPending: isGettingInvoice } = useGetInvoice();

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString("pt-BR", {
			day: "2-digit",
			month: "2-digit",
			year: "numeric",
		});
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat("pt-BR", {
			style: "currency",
			currency: "BRL",
			minimumFractionDigits: 2,
			maximumFractionDigits: 2,
		}).format(amount);
	};

	const getStatusBadge = (status?: string) => {
		switch (status) {
			case "active":
			case "COMPLETED":
				return <Badge className="bg-green-100 text-green-800 border-green-200">Ativo</Badge>;
			case "completed":
				return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Concluído</Badge>;
			case "PENDING":
				return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Pendente</Badge>;
			case "FAILED":
				return <Badge className="bg-red-100 text-red-800 border-red-200">Falhou</Badge>;
			case "REFUNDED":
				return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Reembolsado</Badge>;
			case "cancelled":
				return <Badge className="bg-red-100 text-red-800 border-red-200">Cancelado</Badge>;
			default:
				return <Badge className="bg-gray-100 text-gray-800 border-gray-200">{status}</Badge>;
		}
	};

	const getTypeBadge = (type: string) => {
		switch (type) {
			case "SUBSCRIPTION":
				return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Assinatura</Badge>;
			case "ONE_TIME":
				return <Badge className="bg-orange-100 text-orange-800 border-orange-200">Compra única</Badge>;
			default:
				return <Badge>{type}</Badge>;
		}
	};

	const handleCancelSubscription = (purchase: Purchase) => {
		if (!purchase.subscriptionId) {
			toast.error("ID da assinatura não encontrado");
			return;
		}

		if (confirm("Tem certeza que deseja cancelar esta assinatura?")) {
			cancelSubscription(purchase.subscriptionId);
		}
	};

	const handleViewInvoice = (purchaseId: string) => {
		getInvoice(purchaseId);
	};

	const isLoading = isLoadingPurchases || isLoadingUserPurchases;

	return (
		<>
			{/* Resumo das Assinaturas */}
			<SettingsItem
				title="Resumo das assinaturas"
				description="Visualize o status das suas assinaturas e próximos pagamentos"
			>
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<div className="p-4 border border-border rounded-lg bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-muted-foreground mb-1">Assinaturas ativas</p>
								<p className="text-2xl font-bold text-foreground">
									{isLoading ? "..." : activeSubscriptions}
								</p>
							</div>
							<div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30">
								<PackageIcon className="h-5 w-5 text-green-600 dark:text-green-400" />
							</div>
						</div>
					</div>

					<div className="p-4 border border-border rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-muted-foreground mb-1">Total de compras</p>
								<p className="text-2xl font-bold text-foreground">
									{isLoading ? "..." : totalPurchases}
								</p>
							</div>
							<div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30">
								<ReceiptIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
							</div>
						</div>
					</div>

					<div className="p-4 border border-border rounded-lg bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
						<div className="flex items-center justify-between">
							<div>
								<p className="text-sm font-medium text-muted-foreground mb-1">Próximo pagamento</p>
								<p className="text-2xl font-bold text-foreground">
									{isLoading ? "..." : nextPaymentDate}
								</p>
							</div>
							<div className="p-2 rounded-full bg-orange-100 dark:bg-orange-900/30">
								<CalendarIcon className="h-5 w-5 text-orange-600 dark:text-orange-400" />
							</div>
						</div>
					</div>
				</div>
			</SettingsItem>

			{/* Histórico de Compras */}
			<SettingsItem
				title="Histórico de compras"
				description="Visualize todas as suas compras, assinaturas e faturas"
			>
				<div className="space-y-4">
					{isLoading ? (
						<div className="space-y-4">
							{Array.from({ length: 3 }).map((_, i) => (
								<PurchaseItemSkeleton key={i} />
							))}
						</div>
					) : (
						<>
							{/* Assinaturas */}
							{purchases.map((purchase) => (
								<div
									key={purchase.id}
									className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted/30 transition-all duration-200"
								>
									<div className="flex items-center gap-4">
										<div className="p-2 rounded-full bg-primary/10">
											<PackageIcon className="h-4 w-4 text-primary" />
										</div>
										<div className="space-y-1">
											<div className="flex items-center gap-3">
												<h3 className="font-semibold text-foreground">Assinatura Premium</h3>
												{getTypeBadge(purchase.type)}
												{getStatusBadge(purchase.status)}
											</div>
											<div className="text-sm text-muted-foreground">
												<p>ID: {purchase.id} • Criado em {formatDate(purchase.createdAt)}</p>
												{purchase.subscriptionId && (
													<p>Assinatura: {purchase.subscriptionId}</p>
												)}
											</div>
										</div>
									</div>
									<div className="flex items-center gap-2">
										<Button
											variant="outline"
											size="sm"
											onClick={() => handleViewInvoice(purchase.id)}
											className="flex items-center gap-2"
											disabled={isGettingInvoice}
										>
											{isGettingInvoice ? (
												<Loader2 className="h-4 w-4 animate-spin" />
											) : (
												<EyeIcon className="h-4 w-4" />
											)}
											Ver fatura
										</Button>
										{purchase.type === "SUBSCRIPTION" && purchase.status === "active" && (
											<Button
												variant="outline"
												size="sm"
												onClick={() => handleCancelSubscription(purchase)}
												className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300 flex items-center gap-2"
												disabled={isCancelling}
											>
												{isCancelling ? (
													<Loader2 className="h-4 w-4 animate-spin" />
												) : (
													<XIcon className="h-4 w-4" />
												)}
												Cancelar
											</Button>
										)}
									</div>
								</div>
							))}

							{/* Compras de Cursos */}
							{userPurchases.map((userPurchase) => (
								<div
									key={userPurchase.id}
									className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted/30 transition-all duration-200"
								>
									<div className="flex items-center gap-4">
										<div className="p-2 rounded-full bg-primary/10">
											<DollarSignIcon className="h-4 w-4 text-primary" />
										</div>
										<div className="space-y-1">
											<div className="flex items-center gap-3">
												<h3 className="font-semibold text-foreground">
													{userPurchase.section.title}
												</h3>
												<Badge className="bg-orange-100 text-orange-800 border-orange-200">Compra única</Badge>
												{getStatusBadge(userPurchase.status)}
											</div>
											<div className="text-sm text-muted-foreground">
												<p>ID: {userPurchase.id} • Criado em {formatDate(userPurchase.createdAt)}</p>
												<p>Organização: {userPurchase.section.vitrine.organization.name}</p>
												<p>Valor: {formatCurrency(userPurchase.amount)}</p>
											</div>
										</div>
									</div>
									<div className="flex items-center gap-2">
										<Button
											variant="outline"
											size="sm"
											onClick={() => handleViewInvoice(userPurchase.id)}
											className="flex items-center gap-2"
											disabled={isGettingInvoice}
										>
											{isGettingInvoice ? (
												<Loader2 className="h-4 w-4 animate-spin" />
											) : (
												<EyeIcon className="h-4 w-4" />
											)}
											Ver fatura
										</Button>
									</div>
								</div>
							))}

							{/* Estado vazio */}
							{purchases.length === 0 && userPurchases.length === 0 && (
								<div className="text-center py-8">
									<ReceiptIcon className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
									<h3 className="text-lg font-semibold text-foreground mb-2">
										Nenhuma compra encontrada
									</h3>
									<p className="text-muted-foreground max-w-md mx-auto">
										Você ainda não fez nenhuma compra ou assinatura. Explore nossos cursos e planos disponíveis.
									</p>
								</div>
							)}
						</>
					)}
				</div>
			</SettingsItem>

			{/* Informações de Pagamento */}
			<SettingsItem
				title="Informações de pagamento"
				description="Gerencie seus métodos de pagamento e preferências de fatura"
			>
				<div className="space-y-4">
					<div className="flex items-center justify-between p-4 border border-border rounded-lg">
						<div className="flex items-center gap-4">
							<div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30">
								<CreditCard className="h-4 w-4 text-blue-600 dark:text-blue-400" />
							</div>
							<div>
								<p className="font-medium text-foreground">Método de pagamento</p>
								<p className="text-sm text-muted-foreground">
									Cartão de crédito •••• •••• •••• 4242
								</p>
							</div>
						</div>
						<Button variant="outline" size="sm">
							Alterar
						</Button>
					</div>

					<div className="flex items-center justify-between p-4 border border-border rounded-lg">
						<div className="flex items-center gap-4">
							<div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30">
								<MapPinIcon className="h-4 w-4 text-green-600 dark:text-green-400" />
							</div>
							<div>
								<p className="font-medium text-foreground">Endereço de cobrança</p>
								<p className="text-sm text-muted-foreground">
									Rua das Flores, 123 • São Paulo, SP • 01234-567
								</p>
							</div>
						</div>
						<Button variant="outline" size="sm">
							Alterar
						</Button>
					</div>

					<div className="flex items-center justify-between p-4 border border-border rounded-lg">
						<div className="flex items-center gap-4">
							<div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/30">
								<MailIcon className="h-4 w-4 text-purple-600 dark:text-purple-400" />
							</div>
							<div>
								<p className="font-medium text-foreground">Preferências de fatura</p>
								<p className="text-sm text-muted-foreground">
									Receber faturas por e-mail
								</p>
							</div>
						</div>
						<Button variant="outline" size="sm">
							Configurar
						</Button>
					</div>
				</div>
			</SettingsItem>
		</>
	);
}
