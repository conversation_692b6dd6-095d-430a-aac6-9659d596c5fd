// Tipos baseados no schema Prisma para o módulo de pagamentos

export interface Purchase {
	id: string;
	type: "SUBSCRIPTION" | "ONE_TIME";
	customerId: string;
	subscriptionId?: string;
	productId: string;
	status?: string;
	createdAt: string;
	updatedAt: string;
	organizationId?: string;
	userId?: string;
}

export interface UserPurchase {
	id: string;
	userId: string;
	sectionId: string;
	status: "PENDING" | "COMPLETED" | "FAILED" | "REFUNDED";
	paymentId?: string;
	checkoutUrl?: string;
	amount: number;
	paymentMethod?: string;
	paymentDate?: string;
	expirationDate?: string;
	webhookData?: any;
	createdAt: string;
	updatedAt: string;
	section: {
		id: string;
		title: string;
		vitrine: {
			id: string;
			title: string;
			organization: {
				id: string;
				name: string;
				slug: string;
			};
		};
	};
}

export interface PurchaseStats {
	activeSubscriptions: number;
	totalPurchases: number;
	totalRevenue: number;
	nextPaymentDate: string;
}

export interface PaymentMethod {
	id: string;
	type: "credit_card" | "pix" | "bank_transfer";
	last4?: string;
	brand?: string;
	expiryMonth?: number;
	expiryYear?: number;
	isDefault: boolean;
}

export interface BillingAddress {
	id: string;
	street: string;
	city: string;
	state: string;
	postalCode: string;
	country: string;
}

export interface InvoicePreferences {
	emailNotifications: boolean;
	paperInvoices: boolean;
	taxDocuments: boolean;
}

export interface Invoice {
	id: string;
	purchaseId: string;
	amount: number;
	currency: string;
	status: "PENDING" | "PAID" | "OVERDUE" | "CANCELLED";
	dueDate: string;
	paidDate?: string;
	downloadUrl?: string;
	createdAt: string;
}
