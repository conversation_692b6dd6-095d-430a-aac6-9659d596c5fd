import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import type { Purchase, UserPurchase, PurchaseStats } from "../types";

// Query keys
export const purchasesQueryKey = (userId?: string) =>
	userId ? ["user", "purchases", userId] : ["user", "purchases"];

export const userPurchasesQueryKey = (userId?: string) =>
	userId ? ["user", "user-purchases", userId] : ["user", "user-purchases"];

// Hook para buscar purchases (assinaturas)
export const usePurchases = (userId?: string) => {
	return useQuery({
		queryKey: purchasesQueryKey(userId),
		queryFn: async () => {
			// TODO: Implementar chamada real para API
			// const response = await fetch(`/api/purchases?userId=${userId}`);
			// return response.json();

			// Mock data por enquanto
			return [
				{
					id: "1",
					type: "SUBSCRIPTION" as const,
					customerId: "cus_123456789",
					subscriptionId: "sub_123456789",
					productId: "prod_premium_monthly",
					status: "active",
					createdAt: "2024-01-15T10:00:00Z",
					updatedAt: "2024-01-15T10:00:00Z",
				},
			] as Purchase[];
		},
		enabled: !!userId,
	});
};

// Hook para buscar user purchases (compras de cursos)
export const useUserPurchases = (userId?: string) => {
	return useQuery({
		queryKey: userPurchasesQueryKey(userId),
		queryFn: async () => {
			// TODO: Implementar chamada real para API
			// const response = await fetch(`/api/user-purchases?userId=${userId}`);
			// return response.json();

			// Mock data por enquanto
			return [
				{
					id: "2",
					userId: userId || "",
					sectionId: "section_1",
					status: "COMPLETED" as const,
					paymentId: "pay_123456789",
					amount: 97.00,
					paymentMethod: "credit_card",
					paymentDate: "2024-01-10T14:30:00Z",
					createdAt: "2024-01-10T14:30:00Z",
					updatedAt: "2024-01-10T14:30:00Z",
					section: {
						id: "section_1",
						title: "Curso de Marketing Digital",
						vitrine: {
							id: "vitrine_1",
							title: "Vitrine Principal",
							organization: {
								id: "org_1",
								name: "Academia de Marketing",
								slug: "academia-marketing"
							}
						}
					}
				},
			] as UserPurchase[];
		},
		enabled: !!userId,
	});
};

// Hook para cancelar assinatura
export const useCancelSubscription = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (subscriptionId: string) => {
			// TODO: Implementar chamada real para API
			// const response = await fetch(`/api/purchases/${subscriptionId}/cancel`, {
			//   method: 'POST',
			// });
			// return response.json();

			// Mock por enquanto
			return { success: true };
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: purchasesQueryKey() });
			toast.success("Assinatura cancelada com sucesso!");
		},
		onError: () => {
			toast.error("Erro ao cancelar assinatura");
		},
	});
};

// Hook para buscar fatura
export const useGetInvoice = () => {
	return useMutation({
		mutationFn: async (purchaseId: string) => {
			// TODO: Implementar chamada real para API
			// const response = await fetch(`/api/purchases/${purchaseId}/invoice`);
			// return response.json();

			// Mock por enquanto
			return { url: "#" };
		},
		onSuccess: (data) => {
			if (data.url) {
				window.open(data.url, "_blank");
			}
		},
		onError: () => {
			toast.error("Erro ao buscar fatura");
		},
	});
};

// Helper para calcular estatísticas
export const usePurchaseStats = (userId?: string): PurchaseStats => {
	const { data: purchases = [] } = usePurchases(userId);
	const { data: userPurchases = [] } = useUserPurchases(userId);

	const activeSubscriptions = purchases.filter(p => p.type === "SUBSCRIPTION" && p.status === "active");
	const totalPurchases = purchases.length + userPurchases.length;
	const totalRevenue = userPurchases.reduce((acc, up) => acc + up.amount, 0);

	// Mock para próximo pagamento - deveria vir da API
	const nextPaymentDate = "15/02";

	return {
		activeSubscriptions: activeSubscriptions.length,
		totalPurchases,
		totalRevenue,
		nextPaymentDate,
	};
};
