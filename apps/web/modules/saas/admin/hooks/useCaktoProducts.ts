import { useState, useEffect } from 'react'

interface CaktoProduct {
  id: string
  name: string
  description: string
  image?: string
  price: number
  type: 'course' | 'ebook' | 'mentorship'
  status: 'active' | 'inactive'
  organizationId: string
}

interface UseCaktoProductsOptions {
  organizationId?: string
  type?: string
}

export function useCaktoProducts(options: UseCaktoProductsOptions = {}) {
  const [products, setProducts] = useState<CaktoProduct[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true)
      setError(null)

      try {
        // Mock API call - replace with actual Cakto API
        await new Promise(resolve => setTimeout(resolve, 1000))

        const mockProducts: CaktoProduct[] = [
          {
            id: '1',
            name: 'React Avançado - Cakto',
            description: 'Curso completo de React com conceitos avançados',
            image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=225&fit=crop',
            price: 199.90,
            type: 'course',
            status: 'active',
            organizationId: '1'
          },
          {
            id: '2',
            name: 'JavaScript Moderno',
            description: 'Aprenda JavaScript ES6+ e suas funcionalidades',
            price: 149.90,
            type: 'course',
            status: 'active',
            organizationId: '1'
          },
          {
            id: '3',
            name: 'Next.js Masterclass',
            description: 'Domine o framework Next.js do zero ao avançado',
            image: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=400&h=225&fit=crop',
            price: 299.90,
            type: 'course',
            status: 'active',
            organizationId: '2'
          }
        ]

        let filteredProducts = mockProducts

        if (options.organizationId) {
          filteredProducts = filteredProducts.filter(p => p.organizationId === options.organizationId)
        }

        if (options.type) {
          filteredProducts = filteredProducts.filter(p => p.type === options.type)
        }

        setProducts(filteredProducts)
      } catch (err) {
        setError('Erro ao carregar produtos da Cakto')
      } finally {
        setIsLoading(false)
      }
    }

    fetchProducts()
  }, [options.organizationId, options.type])

  return {
    products,
    isLoading,
    error,
    refetch: () => {
      setIsLoading(true)
      // Re-trigger the effect
    }
  }
}