{"extends": "@repo/tsconfig/nextjs.json", "compilerOptions": {"plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "@analytics": ["./modules/analytics"], "@saas/*": ["./modules/saas/*"], "@ui/*": ["./modules/ui/*"], "@i18n": ["./modules/i18n"], "@i18n/*": ["./modules/i18n/*"], "@shared/*": ["./modules/shared/*"], "content-collections": ["./.content-collections/generated"]}, "jsx": "preserve", "allowJs": true, "esModuleInterop": true, "moduleResolution": "bundler"}, "include": ["**/*.ts", "**/*.tsx", "**/*.cjs", "**/*.mjs", "**/*.mdx", "**/*.md", ".next/types/**/*.ts", ".content-collections/generated/**/*.ts"], "exclude": ["node_modules"]}