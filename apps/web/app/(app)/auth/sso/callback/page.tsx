"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardContent } from "@ui/components/card";
import { LoaderIcon } from "lucide-react";

export default function SSOCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Função para extrair o token do hash da URL
    const extractTokenFromHash = () => {
      // Verifica se estamos no navegador
      if (typeof window === "undefined") return null;

      // Obtém o hash da URL (parte após #)
      const hash = window.location.hash.substring(1);
      const params = new URLSearchParams(hash);

      // Extrai o token de acesso
      return params.get("access_token");
    };

    const processToken = async () => {
      try {
        // Extrai o token do hash
        const token = extractTokenFromHash();

        if (!token) {
          setError("Token de acesso não encontrado na URL");
          return;
        }

        // Armazena o token no localStorage
        localStorage.setItem("cakto_access_token", token);

        // Valida o token com o backend da Cakto
        const caktoApiUrl = process.env.NEXT_PUBLIC_CAKTO_API_URL || "https://api.cakto.com.br";
        const response = await fetch(`${caktoApiUrl}/oauth/userinfo/`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error("Falha na validação do token");
        }

        // Obtém o caminho de redirecionamento armazenado
        const redirectPath = localStorage.getItem("sso_redirect_path") || "/app";

        // Limpa o item do localStorage
        localStorage.removeItem("sso_redirect_path");

        // Redireciona para a página desejada
        router.push(redirectPath);
      } catch (err) {
        console.error("Erro ao processar token SSO:", err);
        setError("Erro ao processar autenticação. Por favor, tente novamente.");
      }
    };

    processToken();
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <Card className="w-full max-w-md">
        <CardContent className="flex flex-col items-center justify-center p-6">
          {error ? (
            <div className="text-center">
              <h2 className="text-lg font-semibold mb-2">Erro de Autenticação</h2>
              <p className="text-muted-foreground mb-4">{error}</p>
              <button
                onClick={() => router.push("/auth/login")}
                className="text-primary hover:underline"
              >
                Voltar para a página de login
              </button>
            </div>
          ) : (
            <div className="text-center">
              <LoaderIcon className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
              <h2 className="text-lg font-semibold mb-2">Processando autenticação</h2>
              <p className="text-muted-foreground">Aguarde enquanto concluímos o processo de login...</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
