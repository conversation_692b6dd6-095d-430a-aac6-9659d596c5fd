import { config } from "@repo/config";
import { getSession } from "@saas/auth/lib/server";
import { SettingsMenu } from "@saas/settings/components/SettingsMenu";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { SidebarContentLayout } from "@saas/shared/components/SidebarContentLayout";
import { UserAvatar } from "@shared/components/UserAvatar";
import {
	CreditCardIcon,
	LockKeyholeIcon,
	SettingsIcon,
	TriangleAlertIcon,
	UserIcon,
	BellIcon,
	ShieldIcon,
} from "lucide-react";
import { getTranslations } from "next-intl/server";
import { redirect } from "next/navigation";
import type { PropsWithChildren } from "react";

export default async function SettingsLayout({ children }: PropsWithChildren) {
	const t = await getTranslations();
	const session = await getSession();

	if (!session) {
		return redirect("/auth/login");
	}

	const menuItems = [
		{
			title: "Minha conta",
			avatar: (
				<UserAvatar
					name={session.user.name ?? ""}
					avatarUrl={session.user.image}
					className="h-16 w-16"
				/>
			),
			items: [
				{
					title: "Informações Pessoais",
					href: "/app/settings/personal",
					icon: <UserIcon className="size-4" />,
				},
				{
					title: "Assinaturas e compras",
					href: "/app/settings/billing",
					icon: <CreditCardIcon className="size-4" />,
				},
				{
					title: "Segurança",
					href: "/app/settings/security",
					icon: <ShieldIcon className="size-4" />,
				},

			],
		},
	];

	return (
		<div>
			{/* Layout com sidebar e conteúdo */}
			<div className="grid grid-cols-1 lg:grid-cols-4 gap-6 lg:gap-8 pb-6 lg:pb-8">
				{/* Sidebar - 1/4 do grid para ser mais larga */}
				<div className="lg:col-span-1">
					<SettingsMenu menuItems={menuItems} />
				</div>

				{/* Conteúdo principal - 3/4 do grid */}
				<div className="lg:col-span-3">
					{children}
				</div>
			</div>
		</div>
	);
}
