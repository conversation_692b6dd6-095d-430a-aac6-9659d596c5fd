"use client";

import { useState, useCallback } from "react";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";
import { Badge } from "@ui/components/badge";
import {
	<PERSON>alog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@ui/components/dialog";
import {
	PlusIcon,
	EditIcon,
	TrashIcon,
	PackageIcon,
	BookOpenIcon,
	GripVerticalIcon,
	ChevronDownIcon,
	ChevronUpIcon,
	PlayIcon,
	FileTextIcon,
	VideoIcon,
	SparklesIcon,
} from "lucide-react";
import { toast } from "sonner";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import type { CourseFormData, CourseModule } from "../types";
import { LessonEditSheet } from "./LessonEditSheet";

interface CourseStructureFormProps {
	data: CourseFormData;
	onUpdate: (data: Partial<CourseFormData>) => void;
	onNext: () => void;
	onPrevious: () => void;
}

interface ModuleFormData {
	name: string;
	description: string;
	cover: string;
}

interface LessonFormData {
	name: string;
	description: string;
	duration: string;
}

interface ExtendedCourseModule extends CourseModule {
	isExpanded?: boolean;
	lessons?: Array<{
		id: string;
		name: string;
		description?: string;
		duration?: string;
		position: number;
	}>;
}

const MODULE_TEMPLATES = [
	{
		id: "introduction",
		name: "Módulo de Introdução",
		description: "Apresentação do curso e conceitos básicos",
		lessons: [
			{ name: "Bem-vindos ao curso", description: "Apresentação e objetivos" },
			{ name: "Como funciona o curso", description: "Metodologia e estrutura" },
			{ name: "Recursos necessários", description: "Ferramentas e materiais" },
		],
	},
	{
		id: "practical",
		name: "Módulo Prático",
		description: "Exercícios e projetos hands-on",
		lessons: [
			{ name: "Projeto prático", description: "Exercício guiado" },
			{ name: "Resolução de problemas", description: "Casos de estudo" },
			{ name: "Exercícios extras", description: "Prática adicional" },
		],
	},
	{
		id: "advanced",
		name: "Módulo Avançado",
		description: "Conceitos avançados e técnicas especializadas",
		lessons: [
			{ name: "Técnicas avançadas", description: "Métodos especializados" },
			{ name: "Casos complexos", description: "Cenários do mundo real" },
			{ name: "Melhores práticas", description: "Dicas e truques" },
		],
	},
];

export function CourseStructureForm({ data, onUpdate, onNext, onPrevious }: CourseStructureFormProps) {
	const [modules, setModules] = useState<ExtendedCourseModule[]>(
		data.modules.map(module => ({ ...module, isExpanded: false, lessons: [] }))
	);
	const [isModuleDialogOpen, setIsModuleDialogOpen] = useState(false);
	const [isLessonDialogOpen, setIsLessonDialogOpen] = useState(false);
	const [isLessonSheetOpen, setIsLessonSheetOpen] = useState(false);
	const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false);
	const [editingModule, setEditingModule] = useState<ExtendedCourseModule | null>(null);
	const [editingLesson, setEditingLesson] = useState<{ moduleId: string; lesson?: any } | null>(null);
	const [moduleFormData, setModuleFormData] = useState<ModuleFormData>({
		name: "",
		description: "",
		cover: "",
	});
	const [lessonFormData, setLessonFormData] = useState<LessonFormData>({
		name: "",
		description: "",
		duration: "",
	});

	const resetModuleForm = useCallback(() => {
		setModuleFormData({
			name: "",
			description: "",
			cover: "",
		});
		setEditingModule(null);
	}, []);

	const resetLessonForm = useCallback(() => {
		setLessonFormData({
			name: "",
			description: "",
			duration: "",
		});
		setEditingLesson(null);
	}, []);

	const toggleModuleExpanded = useCallback((moduleId: string) => {
		setModules(prev =>
			prev.map(module =>
				module.id === moduleId
					? { ...module, isExpanded: !module.isExpanded }
					: module
			)
		);
	}, []);

	const openCreateModuleDialog = useCallback(() => {
		resetModuleForm();
		setIsModuleDialogOpen(true);
	}, [resetModuleForm]);

	const openEditModuleDialog = useCallback((module: ExtendedCourseModule) => {
		setModuleFormData({
			name: module.name,
			description: module.description || "",
			cover: module.cover || "",
		});
		setEditingModule(module);
		setIsModuleDialogOpen(true);
	}, []);

	const openCreateLessonDialog = useCallback((moduleId: string) => {
		resetLessonForm();
		setEditingLesson({ moduleId });
		setIsLessonDialogOpen(true);
	}, [resetLessonForm]);

	const openEditLessonDialog = useCallback((moduleId: string, lesson: any) => {
		setLessonFormData({
			name: lesson.name,
			description: lesson.description || "",
			duration: lesson.duration || "",
		});
		setEditingLesson({ moduleId, lesson });
		setIsLessonDialogOpen(true);
	}, []);

	const openCreateLessonSheet = useCallback((moduleId: string) => {
		setEditingLesson({ moduleId });
		setIsLessonSheetOpen(true);
	}, []);

	const openEditLessonSheet = useCallback((moduleId: string, lesson: any) => {
		setEditingLesson({ moduleId, lesson });
		setIsLessonSheetOpen(true);
	}, []);

	const handleSaveModule = useCallback(() => {
		if (!moduleFormData.name.trim()) {
			toast.error("Nome do módulo é obrigatório");
			return;
		}

		const moduleData: ExtendedCourseModule = {
			id: editingModule?.id || `module-${Date.now()}`,
			name: moduleFormData.name.trim(),
			description: moduleFormData.description.trim(),
			position: editingModule?.position || modules.length,
			cover: moduleFormData.cover.trim() || undefined,
			isExpanded: !editingModule, // Auto-expandir quando for um módulo novo
			lessons: editingModule?.lessons || [],
		};

		let updatedModules: ExtendedCourseModule[];
		if (editingModule) {
			updatedModules = modules.map((module) =>
				module.id === editingModule.id ? moduleData : module
			);
		} else {
			updatedModules = [...modules, moduleData];
		}

		setModules(updatedModules);
		updateParentData(updatedModules);
		setIsModuleDialogOpen(false);
		resetModuleForm();
		toast.success(editingModule ? "Módulo atualizado!" : "Módulo criado!");
	}, [moduleFormData, editingModule, modules, resetModuleForm]);

		const handleSaveLesson = useCallback(() => {
		if (!lessonFormData.name.trim() || !editingLesson) {
			toast.error("Nome da aula é obrigatório");
			return;
		}

		const lessonData = {
			id: editingLesson.lesson?.id || `lesson-${Date.now()}`,
			name: lessonFormData.name.trim(),
			description: lessonFormData.description.trim(),
			duration: lessonFormData.duration.trim(),
			position: editingLesson.lesson?.position || 0,
		};

		const updatedModules = modules.map(module => {
			if (module.id === editingLesson.moduleId) {
				const lessons = module.lessons || [];
				let updatedLessons;

				if (editingLesson.lesson) {
					updatedLessons = lessons.map(lesson =>
						lesson.id === editingLesson.lesson.id ? lessonData : lesson
					);
				} else {
					lessonData.position = lessons.length;
					updatedLessons = [...lessons, lessonData];
				}

				return { ...module, lessons: updatedLessons };
			}
			return module;
		});

		setModules(updatedModules);
		updateParentData(updatedModules);
		setIsLessonDialogOpen(false);
		resetLessonForm();
		toast.success(editingLesson.lesson ? "Aula atualizada!" : "Aula criada!");
	}, [lessonFormData, editingLesson, modules, resetLessonForm]);

	const handleSaveLessonFromSheet = useCallback((lessonData: any) => {
		if (!editingLesson) return;

		const updatedModules = modules.map(module => {
			if (module.id === editingLesson.moduleId) {
				const lessons = module.lessons || [];
				let updatedLessons;

				if (editingLesson.lesson) {
					updatedLessons = lessons.map(lesson =>
						lesson.id === editingLesson.lesson.id ? lessonData : lesson
					);
				} else {
					lessonData.position = lessons.length;
					updatedLessons = [...lessons, lessonData];
				}

				return { ...module, lessons: updatedLessons };
			}
			return module;
		});

		setModules(updatedModules);
		updateParentData(updatedModules);
		setIsLessonSheetOpen(false);
		setEditingLesson(null);
	}, [editingLesson, modules]);

	const handleDeleteLessonFromSheet = useCallback(() => {
		if (!editingLesson?.lesson) return;

		const updatedModules = modules.map(module => {
			if (module.id === editingLesson.moduleId) {
				const lessons = (module.lessons || []).filter(lesson => lesson.id !== editingLesson.lesson.id);
				return { ...module, lessons };
			}
			return module;
		});

		setModules(updatedModules);
		updateParentData(updatedModules);
		setIsLessonSheetOpen(false);
		setEditingLesson(null);
	}, [editingLesson, modules]);

	const handleDeleteModule = useCallback((moduleId: string) => {
		if (confirm("Tem certeza que deseja excluir este módulo?")) {
			const updatedModules = modules
				.filter((module) => module.id !== moduleId)
				.map((module, index) => ({ ...module, position: index }));
			setModules(updatedModules);
			updateParentData(updatedModules);
			toast.success("Módulo excluído!");
		}
	}, [modules]);

	const handleDeleteLesson = useCallback((moduleId: string, lessonId: string) => {
		if (confirm("Tem certeza que deseja excluir esta aula?")) {
			const updatedModules = modules.map(module => {
				if (module.id === moduleId) {
					const lessons = (module.lessons || []).filter(lesson => lesson.id !== lessonId);
					return { ...module, lessons };
				}
				return module;
			});
			setModules(updatedModules);
			updateParentData(updatedModules);
			toast.success("Aula excluída!");
		}
	}, [modules]);

	const handleDragEnd = useCallback((result: any) => {
		if (!result.destination) return;

		const { source, destination } = result;

		if (source.index === destination.index) return;

		const items = Array.from(modules);
		const [reorderedItem] = items.splice(source.index, 1);
		items.splice(destination.index, 0, reorderedItem);

		const updatedModules = items.map((module, index) => ({
			...module,
			position: index,
		}));

		setModules(updatedModules);
		updateParentData(updatedModules);
	}, [modules]);

	const handleApplyTemplate = useCallback((template: typeof MODULE_TEMPLATES[0]) => {
		const newModule: ExtendedCourseModule = {
			id: `module-${Date.now()}`,
			name: template.name,
			description: template.description,
			position: modules.length,
			isExpanded: true,
			lessons: template.lessons.map((lesson, index) => ({
				id: `lesson-${Date.now()}-${index}`,
				name: lesson.name,
				description: lesson.description,
				position: index,
			})),
		};

		const updatedModules = [...modules, newModule];
		setModules(updatedModules);
		updateParentData(updatedModules);
		setIsTemplateDialogOpen(false);
		toast.success("Template aplicado com sucesso!");
	}, [modules]);

	const updateParentData = useCallback((updatedModules: ExtendedCourseModule[]) => {
		const simpleModules = updatedModules.map(({ isExpanded, lessons, ...module }) => module);
		onUpdate({ modules: simpleModules });
	}, [onUpdate]);

	const canProceed = modules.length > 0;

	return (
		<div className="max-w-6xl mx-auto space-y-6">
			{/* Header Section */}
			<div className="flex items-center justify-between">
				<div>
					<h2 className="text-xl font-semibold flex items-center gap-2">
						<PackageIcon className="h-5 w-5 text-primary" />
						Estrutura e Conteúdo do Curso
					</h2>
					<p className="text-muted-foreground mt-1">
						Organize seus módulos, aulas e faça upload de materiais
					</p>
				</div>
				<div className="flex gap-2">
					<Button variant="outline" onClick={() => setIsTemplateDialogOpen(true)}>
						<SparklesIcon className="mr-2 h-4 w-4" />
						Templates
					</Button>
					<Button onClick={openCreateModuleDialog}>
						<PlusIcon className="mr-2 h-4 w-4" />
						Adicionar Módulo
					</Button>
				</div>
			</div>

			{/* Content Section */}
			<div className="space-y-6">
				{/* Modules Section */}
				<div className="space-y-4">
					{modules.length === 0 ? (
						<div className="text-center py-12">
							<BookOpenIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
							<h3 className="text-lg font-semibold text-foreground mb-2">
								Nenhum módulo criado
							</h3>
							<p className="text-muted-foreground mb-6">
								Comece criando módulos para estruturar seu curso
							</p>
							<div className="flex gap-2 justify-center">
								<Button variant="outline" onClick={() => setIsTemplateDialogOpen(true)}>
									<SparklesIcon className="mr-2 h-4 w-4" />
									Usar Template
								</Button>
								<Button onClick={openCreateModuleDialog}>
									<PlusIcon className="mr-2 h-4 w-4" />
									Criar Primeiro Módulo
								</Button>
							</div>
						</div>
					) : (
						<DragDropContext onDragEnd={handleDragEnd}>
							<Droppable droppableId="modules">
								{(provided) => (
									<div {...provided.droppableProps} ref={provided.innerRef} className="space-y-4">
										{modules.map((module, index) => (
											<Draggable key={module.id!} draggableId={module.id!} index={index}>
												{(provided, snapshot) => (
													<Card
														ref={provided.innerRef}
														{...provided.draggableProps}
														className={`transition-all duration-200 ${
															snapshot.isDragging ? "shadow-lg rotate-2" : "hover:shadow-md"
														}`}
													>
														<CardContent className="p-6">
															<div className="space-y-4">
																{/* Module Header */}
																<div className="flex items-center justify-between">
																	<div className="flex items-center gap-4 flex-1">
																		<div
																			{...provided.dragHandleProps}
																			className="cursor-grab text-muted-foreground hover:text-foreground"
																		>
																			<GripVerticalIcon className="h-5 w-5" />
																		</div>
																		<div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary font-semibold">
																			{index + 1}
																		</div>
																		<div className="flex-1">
																			<h4 className="font-semibold text-foreground text-lg">
																				{module.name}
																			</h4>
																			{module.description && (
																				<p className="text-muted-foreground text-sm mt-1">
																					{module.description}
																				</p>
																			)}
																			<div className="flex items-center gap-4 mt-2">
																				<Badge status="info">
																					{(module.lessons || []).length} aulas
																				</Badge>
																				{module.cover && (
																					<Badge status="muted">
																						Com imagem
																					</Badge>
																				)}
																			</div>
																		</div>
																	</div>

																	<div className="flex items-center gap-2">
																		<Button
																			variant="ghost"
																			size="sm"
																			onClick={() => toggleModuleExpanded(module.id!)}
																		>
																			{module.isExpanded ? (
																				<ChevronUpIcon className="h-4 w-4" />
																			) : (
																				<ChevronDownIcon className="h-4 w-4" />
																			)}
																		</Button>
																		<Button
																			variant="outline"
																			size="sm"
																			onClick={() => openEditModuleDialog(module)}
																		>
																			<EditIcon className="h-4 w-4" />
																		</Button>
																		<Button
																			variant="outline"
																			size="sm"
																			onClick={() => handleDeleteModule(module.id!)}
																			className="text-destructive hover:text-destructive"
																		>
																			<TrashIcon className="h-4 w-4" />
																		</Button>
																	</div>
																</div>

																{/* Lessons Section - Expandable */}
																{module.isExpanded && (
																	<div className="space-y-3 border-t pt-4">
																		<div className="flex items-center justify-between">
																			<h5 className="font-medium text-foreground">Aulas do Módulo</h5>
																			<Button
																				size="sm"
																				variant="outline"
																				onClick={() => openCreateLessonSheet(module.id!)}
																			>
																				<PlusIcon className="mr-2 h-3 w-3" />
																				Adicionar Aula
																			</Button>
																		</div>

																		{(module.lessons || []).length === 0 ? (
																			<div className="text-center py-6 bg-muted/30 rounded-lg border-2 border-dashed">
																				<VideoIcon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
																				<p className="text-sm text-muted-foreground">
																					Nenhuma aula criada ainda
																				</p>
																			</div>
																		) : (
																			<div className="space-y-2">
																				{(module.lessons || []).map((lesson, lessonIndex) => (
																					<div
																						key={lesson.id}
																						className="flex items-center justify-between p-3 bg-muted/20 rounded-lg border hover:bg-muted/40 transition-colors"
																					>
																						<div className="flex items-center gap-3">
																							<div className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary text-xs font-medium">
																								{lessonIndex + 1}
																							</div>
																							<PlayIcon className="h-4 w-4 text-muted-foreground" />
																							<div>
																								<p className="font-medium text-sm">{lesson.name}</p>
																								{lesson.description && (
																									<p className="text-xs text-muted-foreground">
																										{lesson.description}
																									</p>
																								)}
																							</div>
																						</div>
																						<div className="flex items-center gap-2">
																							{lesson.duration && (
																								<Badge status="muted" className="text-xs">
																									{lesson.duration}
																								</Badge>
																							)}
																							<Button
																								variant="outline"
																								size="sm"
																								onClick={() => openEditLessonSheet(module.id!, lesson)}
																								className="text-primary hover:text-primary-foreground hover:bg-primary"
																							>
																								<EditIcon className="h-3 w-3 mr-1" />
																								Editar Conteúdo
																							</Button>
																							<Button
																								variant="ghost"
																								size="sm"
																								onClick={() => handleDeleteLesson(module.id!, lesson.id)}
																								className="text-destructive hover:text-destructive"
																							>
																								<TrashIcon className="h-3 w-3" />
																							</Button>
																						</div>
																					</div>
																				))}
																			</div>
																		)}
																	</div>
																)}
															</div>
														</CardContent>
													</Card>
												)}
											</Draggable>
										))}
										{provided.placeholder}
									</div>
								)}
							</Droppable>
						</DragDropContext>
					)}
				</div>


			</div>

			{/* Template Selection Dialog */}
			<Dialog open={isTemplateDialogOpen} onOpenChange={setIsTemplateDialogOpen}>
				<DialogContent className="max-w-2xl">
					<DialogHeader>
						<DialogTitle className="flex items-center gap-2">
							<SparklesIcon className="h-5 w-5" />
							Templates de Módulos
						</DialogTitle>
					</DialogHeader>
					<div className="grid gap-4">
						{MODULE_TEMPLATES.map((template) => (
							<Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow">
								<CardContent className="p-4">
									<div className="flex items-start justify-between">
										<div className="flex-1">
											<h4 className="font-semibold mb-1">{template.name}</h4>
											<p className="text-sm text-muted-foreground mb-3">
												{template.description}
											</p>
											<div className="space-y-1">
												{template.lessons.map((lesson, index) => (
													<div key={index} className="flex items-center gap-2 text-xs text-muted-foreground">
														<PlayIcon className="h-3 w-3" />
														{lesson.name}
													</div>
												))}
											</div>
										</div>
										<Button
											size="sm"
											onClick={() => handleApplyTemplate(template)}
										>
											Usar Template
										</Button>
									</div>
								</CardContent>
							</Card>
						))}
					</div>
				</DialogContent>
			</Dialog>

			{/* Module Form Dialog */}
			<Dialog open={isModuleDialogOpen} onOpenChange={setIsModuleDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							{editingModule ? "Editar Módulo" : "Novo Módulo"}
						</DialogTitle>
					</DialogHeader>
					<div className="space-y-4">
						<div className="space-y-2">
							<label className="text-sm font-medium">Nome do Módulo *</label>
							<Input
								placeholder="Ex: Introdução ao React"
								value={moduleFormData.name}
								onChange={(e) =>
									setModuleFormData((prev) => ({ ...prev, name: e.target.value }))
								}
							/>
						</div>
						<div className="space-y-2">
							<label className="text-sm font-medium">Descrição</label>
							<Textarea
								placeholder="Descreva o conteúdo e objetivos deste módulo"
								value={moduleFormData.description}
								onChange={(e) =>
									setModuleFormData((prev) => ({ ...prev, description: e.target.value }))
								}
								rows={3}
							/>
						</div>
						<div className="space-y-2">
							<label className="text-sm font-medium">Imagem de Capa</label>
							<Input
								placeholder="https://exemplo.com/capa.jpg"
								value={moduleFormData.cover}
								onChange={(e) =>
									setModuleFormData((prev) => ({ ...prev, cover: e.target.value }))
								}
							/>
							<p className="text-xs text-muted-foreground">
								Opcional: URL da imagem de capa do módulo
							</p>
						</div>
						<div className="flex justify-end gap-2 pt-4">
							<Button
								variant="outline"
								onClick={() => setIsModuleDialogOpen(false)}
							>
								Cancelar
							</Button>
							<Button onClick={handleSaveModule}>
								{editingModule ? "Salvar" : "Criar"}
							</Button>
						</div>
					</div>
				</DialogContent>
			</Dialog>

			{/* Lesson Form Dialog */}
			<Dialog open={isLessonDialogOpen} onOpenChange={setIsLessonDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>
							{editingLesson?.lesson ? "Editar Aula" : "Nova Aula"}
						</DialogTitle>
					</DialogHeader>
					<div className="space-y-4">
						<div className="space-y-2">
							<label className="text-sm font-medium">Nome da Aula *</label>
							<Input
								placeholder="Ex: Como criar componentes"
								value={lessonFormData.name}
								onChange={(e) =>
									setLessonFormData((prev) => ({ ...prev, name: e.target.value }))
								}
							/>
						</div>
						<div className="space-y-2">
							<label className="text-sm font-medium">Descrição</label>
							<Textarea
								placeholder="Descreva o conteúdo desta aula"
								value={lessonFormData.description}
								onChange={(e) =>
									setLessonFormData((prev) => ({ ...prev, description: e.target.value }))
								}
								rows={3}
							/>
						</div>
						<div className="space-y-2">
							<label className="text-sm font-medium">Duração</label>
							<Input
								placeholder="Ex: 15 min"
								value={lessonFormData.duration}
								onChange={(e) =>
									setLessonFormData((prev) => ({ ...prev, duration: e.target.value }))
								}
							/>
							<p className="text-xs text-muted-foreground">
								Duração estimada da aula
							</p>
						</div>
						<div className="flex justify-end gap-2 pt-4">
							<Button
								variant="outline"
								onClick={() => setIsLessonDialogOpen(false)}
							>
								Cancelar
							</Button>
							<Button onClick={handleSaveLesson}>
								{editingLesson?.lesson ? "Salvar" : "Criar"}
							</Button>
						</div>
					</div>
				</DialogContent>
			</Dialog>

			{/* Lesson Edit Sheet */}
			<LessonEditSheet
				isOpen={isLessonSheetOpen}
				onClose={() => {
					setIsLessonSheetOpen(false);
					setEditingLesson(null);
				}}
				lesson={editingLesson?.lesson || null}
				moduleId={editingLesson?.moduleId || ''}
				organizationId={data.organizationId}
				onSave={handleSaveLessonFromSheet}
				onDelete={editingLesson?.lesson ? handleDeleteLessonFromSheet : undefined}
			/>
		</div>
	);
}
