"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Progress } from "@ui/components/progress";
import {
	ChevronLeftIcon,
	ChevronRightIcon,
	CheckIcon,
	SaveIcon,
	EyeIcon,
	ArrowLeftIcon,
} from "lucide-react";
import { toast } from "sonner";
import { AdminPageLayout } from "@saas/admin/component/shared/AdminPageLayout";
import { CourseBasicForm } from "../../components/CourseBasicForm";
import { CourseStructureForm } from "../../components/CourseStructureForm";
import { CourseSettingsForm } from "../../components/CourseSettingsForm";
import { CoursePreview } from "../../components/CoursePreview";
import type { CourseFormData } from "../../types";
import { apiClient } from "@shared/lib/api-client";

const STEPS = [
	{
		id: "basic",
		title: "Informações",
		description: "Nome, descrição e organização",
	},
	{
		id: "structure",
		title: "Conteúdo",
		description: "Módulos, aulas e upload de materiais",
	},
	{
		id: "settings",
		title: "Configurações",
		description: "Configurações de acesso e publicação",
	},
	{
		id: "preview",
		title: "Visualizar e Salvar",
		description: "Revise e salve suas alterações",
	},
];

export default function EditCoursePage() {
	const router = useRouter();
	const params = useParams();
	const courseId = params.courseId as string;

	const [currentStep, setCurrentStep] = useState(0);
	const [isLoading, setIsLoading] = useState(false);
	const [isLoadingCourse, setIsLoadingCourse] = useState(true);
	const [formData, setFormData] = useState<CourseFormData>({
		name: "",
		description: "",
		organizationId: "",
		community: "",
		link: "",
		logo: "",
		modules: [],
	});

	// Load course data on mount
	useEffect(() => {
		const loadCourse = async () => {
			try {
				setIsLoadingCourse(true);

				// In a real implementation, you'd fetch the course data from the API
				// For now, we'll set some default data
				// TODO: Implement actual course fetching
				setFormData({
					name: "Curso Exemplo",
					description: "Descrição do curso exemplo",
					organizationId: "org-1",
					community: "Comunidade Exemplo",
					link: "",
					logo: "",
					modules: [],
				});
			} catch (error) {
				console.error("Error loading course:", error);
				toast.error("Erro ao carregar dados do curso");
				router.push("/app/admin/courses");
			} finally {
				setIsLoadingCourse(false);
			}
		};

		if (courseId) {
			loadCourse();
		}
	}, [courseId, router]);

	const updateFormData = (updates: Partial<CourseFormData>) => {
		setFormData((prev: CourseFormData) => ({ ...prev, ...updates }));
	};

	const handleNext = () => {
		if (currentStep < STEPS.length - 1) {
			setCurrentStep((prev) => prev + 1);
		}
	};

	const handlePrevious = () => {
		if (currentStep > 0) {
			setCurrentStep((prev) => prev - 1);
		}
	};

	const handleSave = async () => {
		setIsLoading(true);
		try {
			// Transform modules to match API schema
			const transformedModules = formData.modules.map((module: any, index: number) => ({
				name: module.name,
				position: module.position || index,
				cover: module.cover,
			}));

			const payload = {
				name: formData.name,
				description: formData.description,
				organizationId: formData.organizationId,
				community: formData.community,
				link: formData.link,
				logo: formData.logo,
				modules: transformedModules,
			};

			// TODO: Implement actual course update API call
			// const response = await apiClient.courses[":courseId"].$put({
			//   param: { courseId },
			//   json: payload,
			// });

			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 1000));

			toast.success("Curso atualizado com sucesso!");
			router.push("/app/admin/courses");
		} catch (error) {
			toast.error(error instanceof Error ? error.message : "Erro ao atualizar curso");
			console.error("Error updating course:", error);
		} finally {
			setIsLoading(false);
		}
	};

	const isStepValid = (stepIndex: number) => {
		switch (stepIndex) {
			case 0: // Basic info
				return formData.name && formData.organizationId;
			case 1: // Structure and Content
				return formData.modules.length > 0;
			case 2: // Settings
				return true;
			case 3: // Preview
				return true;
			default:
				return false;
		}
	};

	const canProceed = isStepValid(currentStep);
	const progress = ((currentStep + 1) / STEPS.length) * 100;

	const renderStepContent = () => {
		switch (currentStep) {
			case 0:
				return (
					<CourseBasicForm
						data={formData}
						onUpdate={updateFormData}
						onNext={handleNext}
					/>
				);
			case 1:
				return (
					<CourseStructureForm
						data={formData}
						onUpdate={updateFormData}
						onNext={handleNext}
						onPrevious={handlePrevious}
					/>
				);
			case 2:
				return (
					<CourseSettingsForm
						data={formData}
						onUpdate={updateFormData}
						onNext={handleNext}
						onPrevious={handlePrevious}
					/>
				);
			case 3:
				return (
					<CoursePreview
						data={formData}
						onPrevious={handlePrevious}
					/>
				);
			default:
				return null;
		}
	};

	if (isLoadingCourse) {
		return (
			<AdminPageLayout
				title="Carregando..."
				subtitle="Aguarde enquanto carregamos os dados do curso"
			>
				<Card>
					<CardContent className="py-12">
						<div className="flex items-center justify-center">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
							<span className="ml-2 text-muted-foreground">Carregando curso...</span>
						</div>
					</CardContent>
				</Card>
			</AdminPageLayout>
		);
	}

	return (
		<AdminPageLayout
			title="Editar Curso"
			subtitle="Atualize as informações do seu curso"
			actionButton={{
				label: "Voltar",
				onClick: () => router.push("/app/admin/courses"),
				icon: <ArrowLeftIcon className="mr-2 h-4 w-4" />,
			}}
		>
			{/* Progress Header */}
			<Card className="mb-8">
				<CardHeader className="pb-4">
					<div className="flex items-center justify-between mb-4">
						<div>
							<CardTitle className="text-lg">
								Etapa {currentStep + 1} de {STEPS.length}
							</CardTitle>
							<p className="text-muted-foreground text-sm mt-1">
								{STEPS[currentStep].description}
							</p>
						</div>
						<Badge status="info">
							{Math.round(progress)}% concluído
						</Badge>
					</div>
					<Progress value={progress} className="h-2" />
				</CardHeader>
				<CardContent className="pt-0">
					<div className="flex items-center justify-between">
						{STEPS.map((step, index) => (
							<div
								key={step.id}
								className="flex items-center gap-3"
							>
								<div
									className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
										index < currentStep
											? "bg-green-500 border-green-500 text-white"
											: index === currentStep
											? "bg-primary border-primary text-white"
											: "bg-background border-border text-muted-foreground"
									}`}
								>
									{index < currentStep ? (
										<CheckIcon className="h-4 w-4" />
									) : (
										<span className="text-sm font-medium">
											{index + 1}
										</span>
									)}
								</div>
								<div className="hidden sm:block">
									<p
										className={`font-medium text-sm ${
											index <= currentStep
												? "text-foreground"
												: "text-muted-foreground"
										}`}
									>
										{step.title}
									</p>
								</div>
								{index < STEPS.length - 1 && (
									<div
										className={`hidden sm:block w-12 h-px mx-4 ${
											index < currentStep
												? "bg-green-500"
												: "bg-border"
										}`}
									/>
								)}
							</div>
						))}
					</div>
				</CardContent>
			</Card>

			{/* Step Content */}
			<div className="min-h-[600px]">
				{renderStepContent()}
			</div>

			{/* Navigation Footer */}
			<Card className="mt-8">
				<CardContent className="py-6">
					<div className="flex items-center justify-between">
						<div className="flex gap-2">
							{currentStep > 0 && (
								<Button
									variant="outline"
									onClick={handlePrevious}
									disabled={isLoading}
								>
									<ChevronLeftIcon className="mr-2 h-4 w-4" />
									Anterior
								</Button>
							)}
						</div>

						<div className="flex gap-2">
							<Button
								variant="outline"
								onClick={handleSave}
								disabled={isLoading || !formData.name}
							>
								<SaveIcon className="mr-2 h-4 w-4" />
								Salvar Alterações
							</Button>

							{currentStep < STEPS.length - 1 ? (
								<Button
									onClick={handleNext}
									disabled={!canProceed || isLoading}
								>
									Próximo
									<ChevronRightIcon className="ml-2 h-4 w-4" />
								</Button>
							) : (
								<Button
									onClick={handleSave}
									disabled={!canProceed || isLoading}
									className="bg-green-600 hover:bg-green-700"
								>
									<CheckIcon className="mr-2 h-4 w-4" />
									Finalizar Edição
								</Button>
							)}
						</div>
					</div>
				</CardContent>
			</Card>
		</AdminPageLayout>
	);
}
