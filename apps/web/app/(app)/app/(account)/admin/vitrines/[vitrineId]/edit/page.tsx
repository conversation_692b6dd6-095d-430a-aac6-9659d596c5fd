"use client";

import { useState, useC<PERSON>back, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Progress } from "@ui/components/progress";
import {
	ChevronLeftIcon,
	ChevronRightIcon,
	CheckIcon,
	SaveIcon,
	EyeIcon,
	PlusIcon,
	LoaderIcon,
	AlertCircleIcon,
} from "lucide-react";
import { toast } from "sonner";
import { AdminPageLayout } from "@saas/admin/component/shared/AdminPageLayout";
import { VitrineForm } from "../../components/VitrineForm";
import { SectionManager } from "../../components/SectionManager";
import { CourseSelector } from "../../components/CourseSelector";
import { VitrinePreview } from "../../components/VitrinePreview";

interface VitrineFormData {
	title: string;
	description: string;
	bannerImage?: string;
	organizationId: string;
	status: "DRAFT" | "PUBLISHED" | "ARCHIVED";
	visibility: "PUBLIC" | "PRIVATE";
	isDefault: boolean;
	sections: VitrineSection[];
}

interface VitrineSection {
	id: string;
	title: string;
	subtitle?: string;
	description?: string;
	position: number;
	isLocked: boolean;
	requiresPurchase: boolean;
	checkoutUrl?: string;
	webhookUrl?: string;
	price?: number;
	originalPrice?: number;
	accessType: "FREE" | "PAID" | "MEMBER_ONLY";
	visibility: "PUBLIC" | "PRIVATE";
	courses: VitrineSectionCourse[];
}

interface VitrineSectionCourse {
	courseId: string;
	position: number;
	course?: {
		id: string;
		name: string;
		logo?: string;
		community?: string;
	};
}

const STEPS = [
	{
		id: "basic",
		title: "Informações Básicas",
		description: "Título, descrição e configurações gerais",
	},
	{
		id: "sections",
		title: "Gerenciar Seções",
		description: "Adicione e configure seções da vitrine",
	},
	{
		id: "courses",
		title: "Selecionar Cursos",
		description: "Associe cursos às seções criadas",
	},
	{
		id: "preview",
		title: "Visualizar e Publicar",
		description: "Revise e publique sua vitrine",
	},
];

async function fetchVitrineData(vitrineId: string): Promise<VitrineFormData> {
	await new Promise(resolve => setTimeout(resolve, 1000));

	return {
		title: "Cursos de Desenvolvimento Web",
		description: "Aprenda desenvolvimento web moderno com os melhores cursos do mercado",
		bannerImage: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&h=400&fit=crop",
		organizationId: "1",
		status: "DRAFT",
		visibility: "PUBLIC",
		isDefault: false,
		sections: [
			{
				id: "section-1",
				title: "Módulo Básico",
				subtitle: "Fundamentos do desenvolvimento",
				description: "Aprenda os conceitos básicos necessários para começar",
				position: 0,
				isLocked: false,
				requiresPurchase: false,
				accessType: "FREE",
				visibility: "PUBLIC",
				courses: [
					{
						courseId: "1",
						position: 0,
						course: {
							id: "1",
							name: "React Fundamentals",
							logo: "/images/cards/card1.jpg",
							community: "Aprenda React do zero",
						},
					},
					{
						courseId: "2",
						position: 1,
						course: {
							id: "2",
							name: "Node.js Backend",
							logo: "/images/cards/card2.jpg",
							community: "Desenvolvimento backend",
						},
					},
				],
			},
			{
				id: "section-2",
				title: "Módulo Avançado",
				subtitle: "Conceitos avançados",
				description: "Para desenvolvedores que querem se aprofundar",
				position: 1,
				isLocked: true,
				requiresPurchase: true,
				price: 199.99,
				originalPrice: 299.99,
				accessType: "PAID",
				visibility: "PUBLIC",
				courses: [
					{
						courseId: "3",
						position: 0,
						course: {
							id: "3",
							name: "TypeScript Avançado",
							logo: "/images/cards/card3.jpg",
							community: "TypeScript na prática",
						},
					},
				],
			},
		],
	};
}

export default function EditVitrinePage() {
	const router = useRouter();
	const params = useParams();
	const vitrineId = params.vitrineId as string;

	const [currentStep, setCurrentStep] = useState(0);
	const [isLoading, setIsLoading] = useState(false);
	const [isFetching, setIsFetching] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [formData, setFormData] = useState<VitrineFormData>({
		title: "",
		description: "",
		organizationId: "",
		status: "DRAFT",
		visibility: "PUBLIC",
		isDefault: false,
		sections: [],
	});

	// Load existing vitrine data
	useEffect(() => {
		async function loadVitrine() {
			try {
				setIsFetching(true);
				setError(null);
				const data = await fetchVitrineData(vitrineId);
				setFormData(data);
			} catch (err) {
				setError("Erro ao carregar dados da vitrine");
				console.error("Error loading vitrine:", err);
			} finally {
				setIsFetching(false);
			}
		}

		if (vitrineId) {
			loadVitrine();
		}
	}, [vitrineId]);

	const updateFormData = useCallback((updates: Partial<VitrineFormData>) => {
		setFormData((prev) => ({ ...prev, ...updates }));
	}, []);

	const handleNext = useCallback(() => {
		if (currentStep < STEPS.length - 1) {
			setCurrentStep((prev) => prev + 1);
		}
	}, [currentStep]);

	const handlePrevious = useCallback(() => {
		if (currentStep > 0) {
			setCurrentStep((prev) => prev - 1);
		}
	}, [currentStep]);

	const handleSave = useCallback(async (asDraft = true) => {
		setIsLoading(true);
		try {
			const payload = {
				...formData,
				status: asDraft ? "DRAFT" : "PUBLISHED",
			};

			console.log("Updating vitrine:", payload);

			toast.success(
				asDraft
					? "Vitrine atualizada como rascunho!"
					: "Vitrine atualizada e publicada com sucesso!"
			);

			router.push("/app/admin/vitrines");
		} catch (error) {
			toast.error("Erro ao atualizar vitrine");
			console.error("Error updating vitrine:", error);
		} finally {
			setIsLoading(false);
		}
	}, [formData, router]);

	const handleDelete = useCallback(async () => {
		if (!confirm("Tem certeza que deseja excluir esta vitrine? Esta ação não pode ser desfeita.")) {
			return;
		}

		setIsLoading(true);
		try {
			console.log("Deleting vitrine:", vitrineId);

			toast.success("Vitrine excluída com sucesso!");
			router.push("/app/admin/vitrines");
		} catch (error) {
			toast.error("Erro ao excluir vitrine");
			console.error("Error deleting vitrine:", error);
		} finally {
			setIsLoading(false);
		}
	}, [vitrineId, router]);

	const isStepValid = useCallback((stepIndex: number) => {
		switch (stepIndex) {
			case 0:
				return formData.title && formData.organizationId;
			case 1:
				return formData.sections.length > 0;
			case 2:
				return formData.sections.every(section => section.courses.length > 0);
			case 3:
				return true;
			default:
				return false;
		}
	}, [formData]);

	const canProceed = isStepValid(currentStep);
	const progress = ((currentStep + 1) / STEPS.length) * 100;

	if (isFetching) {
		return (
			<AdminPageLayout
				title="Carregando Vitrine"
				subtitle="Aguarde enquanto carregamos os dados..."
			>
				<Card>
					<CardContent className="py-12">
						<div className="flex items-center justify-center">
							<div className="text-center">
								<LoaderIcon className="mx-auto h-8 w-8 animate-spin text-primary mb-4" />
								<h3 className="text-lg font-semibold mb-2">Carregando dados da vitrine</h3>
								<p className="text-muted-foreground">
									Isso pode levar alguns segundos...
								</p>
							</div>
						</div>
					</CardContent>
				</Card>
			</AdminPageLayout>
		);
	}

	if (error) {
		return (
			<AdminPageLayout
				title="Erro ao Carregar"
				subtitle="Não foi possível carregar os dados da vitrine"
			>
				<Card>
					<CardContent className="py-12">
						<div className="flex items-center justify-center">
							<div className="text-center">
								<AlertCircleIcon className="mx-auto h-8 w-8 text-red-500 mb-4" />
								<h3 className="text-lg font-semibold mb-2 text-red-700">Erro ao carregar vitrine</h3>
								<p className="text-muted-foreground mb-6">{error}</p>
								<div className="flex gap-3 justify-center">
									<Button
										variant="outline"
										onClick={() => router.push("/app/admin/vitrines")}
									>
										Voltar à Lista
									</Button>
									<Button onClick={() => window.location.reload()}>
										Tentar Novamente
									</Button>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>
			</AdminPageLayout>
		);
	}

	const renderStepContent = () => {
		switch (currentStep) {
			case 0:
				return (
					<VitrineForm
						data={formData}
						onUpdate={updateFormData}
					/>
				);
			case 1:
				return (
					<SectionManager
						sections={formData.sections as any}
						onUpdate={(sections) => updateFormData({ sections })}
					/>
				);
			case 2:
				return (
					<CourseSelector
						sections={formData.sections as any}
						organizationId={formData.organizationId}
						onUpdate={(sections: any) => updateFormData({ sections })}
					/>
				);
			case 3:
				return (
					<VitrinePreview
						data={formData}
						onSave={handleSave}
						onPrevious={handlePrevious}
						isLoading={isLoading}
					/>
				);
			default:
				return null;
		}
	};

	return (
		<AdminPageLayout
			title={`Editar Vitrine: ${formData.title}`}
			subtitle="Atualize sua vitrine conforme necessário"
			actionButton={{
				label: "Excluir Vitrine",
				onClick: handleDelete,
				icon: <AlertCircleIcon className="mr-2 h-4 w-4" />,
			}}
		>
			{/* Status Badge */}
			<div className="mb-6">
				<div className="flex items-center gap-3">
					<Badge
						className={
							formData.status === "PUBLISHED"
								? "bg-green-100 text-green-800 border-green-200"
								: formData.status === "DRAFT"
								? "bg-yellow-100 text-yellow-800 border-yellow-200"
								: "bg-red-100 text-red-800 border-red-200"
						}
					>
						{formData.status === "PUBLISHED"
							? "Publicado"
							: formData.status === "DRAFT"
							? "Rascunho"
							: "Arquivado"}
					</Badge>
					<Badge
						className={
							formData.visibility === "PUBLIC"
								? "bg-blue-100 text-blue-800 border-blue-200"
								: "bg-purple-100 text-purple-800 border-purple-200"
						}
					>
						{formData.visibility === "PUBLIC" ? "Público" : "Privado"}
					</Badge>
				</div>
			</div>

			{/* Progress Header */}
			<Card className="mb-8">
				<CardHeader className="pb-4">
					<div className="flex items-center justify-between mb-4">
						<div>
							<CardTitle className="text-lg">
								Etapa {currentStep + 1} de {STEPS.length}
							</CardTitle>
							<p className="text-muted-foreground text-sm mt-1">
								{STEPS[currentStep].description}
							</p>
						</div>
						<Badge className="bg-primary/10 text-primary border-primary/20">
							{Math.round(progress)}% concluído
						</Badge>
					</div>
					<Progress value={progress} className="h-2" />
				</CardHeader>
				<CardContent className="pt-0">
					<div className="flex items-center justify-between">
						{STEPS.map((step, index) => (
							<div
								key={step.id}
								className="flex items-center gap-3 cursor-pointer"
								onClick={() => setCurrentStep(index)}
							>
								<div
									className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
										index < currentStep
											? "bg-green-500 border-green-500 text-white"
											: index === currentStep
											? "bg-primary border-primary text-white"
											: "bg-background border-border text-muted-foreground hover:border-primary/50"
									}`}
								>
									{index < currentStep ? (
										<CheckIcon className="h-4 w-4" />
									) : (
										<span className="text-sm font-medium">
											{index + 1}
										</span>
									)}
								</div>
								<div className="hidden sm:block">
									<p
										className={`font-medium text-sm ${
											index <= currentStep
												? "text-foreground"
												: "text-muted-foreground"
										}`}
									>
										{step.title}
									</p>
								</div>
								{index < STEPS.length - 1 && (
									<div
										className={`hidden sm:block w-12 h-px mx-4 ${
											index < currentStep
												? "bg-green-500"
												: "bg-border"
										}`}
									/>
								)}
							</div>
						))}
					</div>
				</CardContent>
			</Card>

			{/* Step Content */}
			<div className="min-h-[600px]">
				{renderStepContent()}
			</div>

			{/* Navigation Footer */}
			<Card className="mt-8">
				<CardContent className="py-6">
					<div className="flex items-center justify-between">
						<div className="flex gap-2">
							{currentStep > 0 && (
								<Button
									variant="outline"
									onClick={handlePrevious}
									disabled={isLoading}
								>
									<ChevronLeftIcon className="mr-2 h-4 w-4" />
									Anterior
								</Button>
							)}
						</div>

						<div className="flex gap-2">
							<Button
								variant="outline"
								onClick={() => handleSave(true)}
								disabled={isLoading || !formData.title}
							>
								<SaveIcon className="mr-2 h-4 w-4" />
								Salvar Alterações
							</Button>

							{currentStep < STEPS.length - 1 ? (
								<Button
									onClick={handleNext}
									disabled={!canProceed || isLoading}
								>
									Próximo
									<ChevronRightIcon className="ml-2 h-4 w-4" />
								</Button>
							) : (
								<div className="flex gap-2">
									<Button
										variant="outline"
										onClick={() => {
											// TODO: Open preview in new tab
											toast.info("Visualização em nova aba");
										}}
									>
										<EyeIcon className="mr-2 h-4 w-4" />
										Visualizar
									</Button>
									<Button
										onClick={() => handleSave(false)}
										disabled={!canProceed || isLoading}
										className="bg-green-600 hover:bg-green-700"
									>
										<CheckIcon className="mr-2 h-4 w-4" />
										Atualizar e Publicar
									</Button>
								</div>
							)}
						</div>
					</div>
				</CardContent>
			</Card>
		</AdminPageLayout>
	);
}
