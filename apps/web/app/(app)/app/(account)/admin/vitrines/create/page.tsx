"use client";

import { useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Progress } from "@ui/components/progress";
import {
	ChevronLeftIcon,
	ChevronRightIcon,
	CheckIcon,
	SaveIcon,
	EyeIcon,
	PlusIcon,
} from "lucide-react";
import { toast } from "sonner";
import { AdminPageLayout } from "@saas/admin/component/shared/AdminPageLayout";
import { VitrineForm } from "../components/VitrineForm";
import { SectionManager } from "../components/SectionManager";
import { CourseSelector } from "../components/CourseSelector";
import { VitrinePreview } from "./VitrinePreview";
import { VitrinePreviewModal } from "./VitrinePreviewModal";
import type { VitrineFormData } from "./types";
import { apiClient } from "@shared/lib/api-client";

const STEPS = [
	{
		id: "basic",
		title: "Informações Básicas",
		description: "Título, descrição e configurações gerais",
	},
	{
		id: "sections",
		title: "Gerenciar Seções",
		description: "Adicione e configure seções da vitrine",
	},
	{
		id: "courses",
		title: "Selecionar Cursos",
		description: "Associe cursos às seções criadas",
	},
	{
		id: "preview",
		title: "Visualizar e Publicar",
		description: "Revise e publique sua vitrine",
	},
];

export default function CreateVitrinePage() {
	const router = useRouter();
	const [currentStep, setCurrentStep] = useState(0);
	const [isLoading, setIsLoading] = useState(false);
	const [previewModalOpen, setPreviewModalOpen] = useState(false);
	const [formData, setFormData] = useState<VitrineFormData>({
		title: "",
		description: "",
		organizationId: "",
		status: "DRAFT",
		visibility: "PUBLIC",
		isDefault: false,
		sections: [],
	});

	const updateFormData = useCallback((updates: Partial<VitrineFormData>) => {
		setFormData((prev) => ({ ...prev, ...updates }));
	}, []);

	const handleNext = useCallback(() => {
		if (currentStep < STEPS.length - 1) {
			setCurrentStep((prev) => prev + 1);
		}
	}, [currentStep]);

	const handlePrevious = useCallback(() => {
		if (currentStep > 0) {
			setCurrentStep((prev) => prev - 1);
		}
	}, [currentStep]);

	const handleSave = useCallback(async (asDraft = true) => {
		setIsLoading(true);
		try {
			// Transform sections to match API schema
			const transformedSections = formData.sections.map((section, index) => ({
				title: section.title,
				subtitle: section.subtitle,
				description: section.description,
				position: section.position || index,
				isLocked: section.isLocked || false,
				requiresPurchase: section.requiresPurchase || false,
				checkoutUrl: section.checkoutUrl,
				webhookUrl: section.webhookUrl,
				price: section.price,
				originalPrice: section.originalPrice,
				accessType: section.accessType || "FREE" as const,
				visibility: section.visibility || "PUBLIC" as const,
				courses: section.courses.map(course => ({
					courseId: course.courseId,
					position: course.position,
				})),
			}));

			const payload = {
				title: formData.title,
				description: formData.description,
				bannerImage: formData.bannerImage,
				organizationId: formData.organizationId,
				status: (asDraft ? "DRAFT" : "PUBLISHED") as "DRAFT" | "PUBLISHED" | "ARCHIVED",
				visibility: formData.visibility,
				isDefault: formData.isDefault,
				sections: transformedSections,
			};

			// Call the API to create vitrine
			const response = await apiClient.vitrines.$post({
				json: payload,
			});

			if (!response.ok) {
				const error = await response.json();
				throw new Error(error.error || "Failed to create vitrine");
			}

			const createdVitrine = await response.json();

			toast.success(
				asDraft
					? "Vitrine salva como rascunho!"
					: "Vitrine criada e publicada com sucesso!"
			);

			router.push("/app/admin/vitrines");
		} catch (error) {
			toast.error(error instanceof Error ? error.message : "Erro ao salvar vitrine");
			console.error("Error creating vitrine:", error);
		} finally {
			setIsLoading(false);
		}
	}, [formData, router]);

	const isStepValid = useCallback((stepIndex: number) => {
		switch (stepIndex) {
			case 0: // Basic info
				return formData.title && formData.organizationId;
			case 1: // Sections
				return formData.sections.length > 0;
			case 2: // Courses
				return formData.sections.every(section => section.courses.length > 0);
			case 3: // Preview
				return true;
			default:
				return false;
		}
	}, [formData]);

	const getStepNavigationLabels = () => {
		switch (currentStep) {
			case 0:
				return {
					previous: null,
					next: "Continuar"
				};
			case 1:
				return {
					previous: "Voltar",
					next: "Continuar"
				};
			case 2:
				return {
					previous: "Voltar",
					next: "Continuar"
				};
			case 3:
				return {
					previous: "Voltar",
					next: null
				};
			default:
				return { previous: null, next: null };
		}
	};

	const canProceed = isStepValid(currentStep);
	const progress = ((currentStep + 1) / STEPS.length) * 100;
	const navigationLabels = getStepNavigationLabels();

	const renderStepContent = () => {
		switch (currentStep) {
			case 0:
				return (
					<VitrineForm
						data={formData}
						onUpdate={updateFormData}
					/>
				);
			case 1:
				return (
					<SectionManager
						sections={formData.sections as any}
						onUpdate={(sections) => updateFormData({ sections: sections as any })}
					/>
				);
			case 2:
				return (
					<CourseSelector
						sections={formData.sections as any}
						organizationId={formData.organizationId}
						onUpdate={(sections) => updateFormData({ sections: sections as any })}
					/>
				);
			case 3:
				return (
					<VitrinePreview
						data={formData}
						onSave={handleSave}
						isLoading={isLoading}
					/>
				);
			default:
				return null;
		}
	};

	return (
		<AdminPageLayout
			title="Criar Nova Vitrine"
			subtitle="Configure sua vitrine em etapas simples"
		>
			{/* Progress Header */}
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle className="text-lg">
								Etapa {currentStep + 1} de {STEPS.length}
							</CardTitle>
							<p className="text-muted-foreground">
								{STEPS[currentStep].description}
							</p>
						</div>
						<Badge className="bg-primary/10 text-primary border-primary/20">
							{Math.round(progress)}% concluído
						</Badge>
					</div>
					<Progress value={progress} className="mt-4" />
				</CardHeader>
				<CardContent className="pt-0">
					<div className="flex items-center justify-between">
						{STEPS.map((step, index) => (
							<div
								key={step.id}
								className="flex items-center gap-3"
							>
								<div
									className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
										index < currentStep
											? "bg-green-500 border-green-500 text-white"
											: index === currentStep
											? "bg-primary border-primary text-white"
											: "bg-background border-border text-muted-foreground"
									}`}
								>
									{index < currentStep ? (
										<CheckIcon className="h-4 w-4" />
									) : (
										<span className="text-sm font-medium">
											{index + 1}
										</span>
									)}
								</div>
								<div className="hidden sm:block">
									<p
										className={`font-medium text-sm ${
											index <= currentStep
												? "text-foreground"
												: "text-muted-foreground"
										}`}
									>
										{step.title}
									</p>
								</div>
								{index < STEPS.length - 1 && (
									<div
										className={`hidden sm:block w-12 h-px mx-4 ${
											index < currentStep
												? "bg-green-500"
												: "bg-border"
										}`}
									/>
								)}
							</div>
						))}
					</div>
				</CardContent>
			</Card>

			{/* Step Content */}
			<div className="min-h-[600px]">
				{renderStepContent()}
			</div>


			<div className="mt-8 border-none">

					<div className="flex items-center justify-between">
						<div className="flex gap-2">
							{navigationLabels.previous && (
								<Button
									variant="outline"
									onClick={handlePrevious}
									disabled={isLoading}
								>
									<ChevronLeftIcon className="mr-2 h-4 w-4" />
									{navigationLabels.previous}
								</Button>
							)}
						</div>

						<div className="flex gap-2">
							<Button
								variant="outline"
								onClick={() => handleSave(true)}
								disabled={isLoading || !formData.title}
							>
								<SaveIcon className="mr-2 h-4 w-4" />
								Salvar rascunho
							</Button>

							{navigationLabels.next ? (
								<Button
									onClick={handleNext}
									disabled={!canProceed || isLoading}
								>
									{navigationLabels.next}
									<ChevronRightIcon className="ml-2 h-4 w-4" />
								</Button>
							) : (
								<div className="flex gap-2">
									<Button
										variant="outline"
										onClick={() => setPreviewModalOpen(true)}
									>
										<EyeIcon className="mr-2 h-4 w-4" />
										Visualizar
									</Button>
									<Button
										onClick={() => handleSave(false)}
										disabled={!canProceed || isLoading}
										className="bg-green-600 hover:bg-green-700"
									>
										<CheckIcon className="mr-2 h-4 w-4" />
										Publicar Vitrine
									</Button>
								</div>
							)}
						</div>
					</div>

			</div>

			{/* Preview Modal */}
			<VitrinePreviewModal
				data={formData}
				isOpen={previewModalOpen}
				onClose={() => setPreviewModalOpen(false)}
			/>
		</AdminPageLayout>
	);
}
