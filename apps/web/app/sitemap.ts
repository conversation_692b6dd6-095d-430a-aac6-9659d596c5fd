import { config } from "@repo/config";
import { getBaseUrl } from "@repo/utils";
import { allLegalPages } from "content-collections";
import type { MetadataRoute } from "next";
import { docsSource } from "./docs-source";

const baseUrl = getBaseUrl();
const locales = config.i18n.enabled
	? Object.keys(config.i18n.locales)
	: [config.i18n.defaultLocale];

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
	return [
		// App routes
		...locales.map((locale) => ({
			url: new URL(`/${locale}/app`, baseUrl).href,
			lastModified: new Date(),
		})),
		// Auth routes
		...locales.map((locale) => ({
			url: new URL(`/${locale}/auth/login`, baseUrl).href,
			lastModified: new Date(),
		})),
		...locales.map((locale) => ({
			url: new URL(`/${locale}/auth/signup`, baseUrl).href,
			lastModified: new Date(),
		})),
		// Legal pages
		...allLegalPages.map((page) => ({
			url: new URL(`/${page.locale}/legal/${page.path}`, baseUrl).href,
			lastModified: new Date(),
		})),
		// Docs pages
		...docsSource.getLanguages().flatMap((locale) =>
			docsSource.getPages(locale.language).map((page) => ({
				url: new URL(
					`/${locale.language}/docs/${page.slugs.join("/")}`,
					baseUrl,
				).href,
				lastModified: new Date(),
			})),
		),
	];
}
