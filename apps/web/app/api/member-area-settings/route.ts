import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";

export async function GET() {
    try {
        const session = await getSession();
        
        if (!session?.user) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Get user's organization (assuming first organization for now)
        const membership = await db.member.findFirst({
            where: { userId: session.user.id },
            include: { organization: true },
        });

        if (!membership) {
            return NextResponse.json({ error: "No organization found" }, { status: 404 });
        }

        const settings = await db.memberAreaSettings.findFirst({
            where: { organizationId: membership.organizationId },
        });

        return NextResponse.json(settings);
    } catch (error) {
        console.error("Error fetching member area settings:", error);
        return NextResponse.json({ error: "Internal server error" }, { status: 500 });
    }
}

export async function PATCH(request: NextRequest) {
    try {
        const session = await getSession();
        
        if (!session?.user) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const data = await request.json();

        // Get user's organization
        const membership = await db.member.findFirst({
            where: { userId: session.user.id },
            include: { organization: true },
        });

        if (!membership) {
            return NextResponse.json({ error: "No organization found" }, { status: 404 });
        }

        const settings = await db.memberAreaSettings.upsert({
            where: { organizationId: membership.organizationId },
            update: data,
            create: {
                ...data,
                organizationId: membership.organizationId,
            },
        });

        return NextResponse.json(settings);
    } catch (error) {
        console.error("Error updating member area settings:", error);
        return NextResponse.json({ error: "Internal server error" }, { status: 500 });
    }
}