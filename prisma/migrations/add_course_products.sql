-- CreateTable
CREATE TABLE "CourseProduct" (
    "id" TEXT NOT NULL,
    "courseId" TEXT NOT NULL,
    "caktoProductId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CourseProduct_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "CourseProduct_caktoProductId_key" ON "CourseProduct"("caktoProductId");

-- CreateIndex
CREATE INDEX "CourseProduct_courseId_idx" ON "CourseProduct"("courseId");

-- AddForeignKey
ALTER TABLE "CourseProduct" ADD CONSTRAINT "CourseProduct_courseId_fkey" FOREIGN KEY ("courseId") REFERENCES "Course"("id") ON DELETE CASCADE ON UPDATE CASCADE;