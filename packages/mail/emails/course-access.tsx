import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Link,
  Preview,
  Text,
} from "@react-email/components";

interface CourseAccessEmailProps {
  name: string;
  courseName: string;
  url: string;
}

export const CourseAccessEmail = ({
  name,
  courseName,
  url,
}: CourseAccessEmailProps) => (
  <Html>
    <Head />
    <Preview>Acesso liberado para {courseName}</Preview>
    <Body style={main}>
      <Container style={container}>
        <Heading style={h1}>Acesso Liberado! 🎉</Heading>
        
        <Text style={text}>
          Olá {name},
        </Text>
        
        <Text style={text}>
          Seu acesso ao curso <strong>{courseName}</strong> foi liberado com sucesso!
        </Text>
        
        <Text style={text}>
          Você já pode começar a estudar e aproveitar todo o conteúdo disponível.
        </Text>
        
        <Text style={text}>
          <Link href={url} style={link}>
            Clique aqui para acessar o curso
          </Link>
        </Text>
        
        <Text style={text}>
          Bons estudos!
        </Text>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: "#ffffff",
  fontFamily: "HelveticaNeue,Helvetica,Arial,sans-serif",
};

const container = {
  backgroundColor: "#ffffff",
  border: "1px solid #eee",
  borderRadius: "5px",
  boxShadow: "0 5px 10px rgba(20,50,70,.2)",
  marginTop: "20px",
  maxWidth: "360px",
  margin: "0 auto",
  padding: "68px 0 130px",
};

const h1 = {
  color: "#000",
  fontSize: "24px",
  fontWeight: "normal",
  textAlign: "center" as const,
  margin: "30px 0",
  padding: "0",
};

const text = {
  color: "#000",
  fontSize: "14px",
  lineHeight: "24px",
  textAlign: "left" as const,
  margin: "16px 32px",
};

const link = {
  color: "#556cd6",
  textDecoration: "underline",
};

export default CourseAccessEmail;