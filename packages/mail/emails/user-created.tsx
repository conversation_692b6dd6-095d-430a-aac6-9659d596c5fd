import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Link,
  Preview,
  Text,
} from "@react-email/components";

interface UserCreatedEmailProps {
  name: string;
  email: string;
  password: string;
  url: string;
}

export const UserCreatedEmail = ({
  name,
  email,
  password,
  url,
}: UserCreatedEmailProps) => (
  <Html>
    <Head />
    <Preview>Sua conta foi criada com sucesso!</Preview>
    <Body style={main}>
      <Container style={container}>
        <Heading style={h1}>Bem-vindo à área de membros!</Heading>
        
        <Text style={text}>
          Olá {name},
        </Text>
        
        <Text style={text}>
          Sua conta foi criada com sucesso! Use as credenciais abaixo para fazer login:
        </Text>
        
        <Container style={credentialsBox}>
          <Text style={credentialLabel}>Email:</Text>
          <Text style={credentialValue}>{email}</Text>
          
          <Text style={credentialLabel}>Senha temporária:</Text>
          <Text style={credentialValue}>{password}</Text>
        </Container>
        
        <Text style={text}>
          <Link href={url} style={link}>
            Clique aqui para acessar a plataforma
          </Link>
        </Text>
        
        <Text style={text}>
          Recomendamos que você altere sua senha após o primeiro login.
        </Text>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: "#ffffff",
  fontFamily: "HelveticaNeue,Helvetica,Arial,sans-serif",
};

const container = {
  backgroundColor: "#ffffff",
  border: "1px solid #eee",
  borderRadius: "5px",
  boxShadow: "0 5px 10px rgba(20,50,70,.2)",
  marginTop: "20px",
  maxWidth: "360px",
  margin: "0 auto",
  padding: "68px 0 130px",
};

const h1 = {
  color: "#000",
  fontSize: "24px",
  fontWeight: "normal",
  textAlign: "center" as const,
  margin: "30px 0",
  padding: "0",
};

const text = {
  color: "#000",
  fontSize: "14px",
  lineHeight: "24px",
  textAlign: "left" as const,
  margin: "16px 32px",
};

const credentialsBox = {
  backgroundColor: "#f6f9fc",
  border: "1px solid #e6ebf1",
  borderRadius: "4px",
  margin: "16px 32px",
  padding: "16px",
};

const credentialLabel = {
  color: "#525f7f",
  fontSize: "12px",
  fontWeight: "bold",
  margin: "0 0 4px 0",
};

const credentialValue = {
  color: "#000",
  fontSize: "14px",
  fontWeight: "bold",
  margin: "0 0 16px 0",
};

const link = {
  color: "#556cd6",
  textDecoration: "underline",
};

export default UserCreatedEmail;