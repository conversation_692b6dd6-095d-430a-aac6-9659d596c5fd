import { Heading, Link, Text, Hr } from "@react-email/components";
import React from "react";
import { createTranslator } from "use-intl/core";
import PrimaryButton from "../src/components/PrimaryButton";
import Wrapper from "../src/components/Wrapper";
import { defaultLocale } from "../src/util/translations";
import { defaultTranslations } from "../src/util/translations";
import type { BaseMailProps } from "../types";

export function UserCreated({
	url,
	name,
	email,
	password,
	locale,
	translations,
}: {
	url: string;
	name: string;
	email: string;
	password: string;
} & BaseMailProps) {
	const t = createTranslator({
		locale,
		messages: translations,
	});

	return (
		<Wrapper>
			<Heading className="text-2xl font-bold text-center mb-6">
				{t("mail.userCreated.headline")}
			</Heading>

			<Text className="text-lg mb-4">
				{t("mail.common.greeting")} {name},
			</Text>

			<Text className="mb-4">
				{t("mail.userCreated.body")}
			</Text>

			<Text className="font-semibold mb-2">
				{t("mail.userCreated.credentials")}
			</Text>

			<div className="bg-gray-50 p-4 rounded-lg mb-4 border">
				<Text className="mb-2 font-mono text-sm">
					{t("mail.userCreated.email", { email })}
				</Text>
				<Text className="mb-0 font-mono text-sm">
					{t("mail.userCreated.password", { password })}
				</Text>
			</div>

			<Text className="mb-4">
				{t("mail.userCreated.changePassword")}
			</Text>

			<PrimaryButton href={url}>
				{t("mail.userCreated.loginButton")}
			</PrimaryButton>

			<Text className="mt-4 text-sm text-muted-foreground">
				{t("mail.common.openLinkInBrowser")}
				<br />
				<Link href={url} className="break-all">{url}</Link>
			</Text>

			<Hr className="my-6" />

			<div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
				<Text className="text-sm text-yellow-800 mb-0">
					<strong>⚠️ Importante:</strong> {t("mail.userCreated.securityNote")}
				</Text>
			</div>

			<Text className="text-sm text-muted-foreground mt-6">
				{t("mail.common.regards")}
				<br />
				{t("mail.common.team")}
			</Text>
		</Wrapper>
	);
}

UserCreated.PreviewProps = {
	locale: defaultLocale,
	translations: defaultTranslations,
	url: "#",
	name: "João Silva",
	email: "<EMAIL>",
	password: "senha123",
};

export default UserCreated;
