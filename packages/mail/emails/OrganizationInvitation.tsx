import { Heading, Link, Text, Hr } from "@react-email/components";
import React from "react";
import { createTranslator } from "use-intl/core";
import PrimaryButton from "../src/components/PrimaryButton";
import Wrapper from "../src/components/Wrapper";
import { defaultLocale } from "../src/util/translations";
import { defaultTranslations } from "../src/util/translations";
import type { BaseMailProps } from "../types";

export function OrganizationInvitation({
	url,
	organizationName,
	inviterName,
	locale,
	translations,
}: {
	url: string;
	organizationName: string;
	inviterName?: string;
} & BaseMailProps) {
	const t = createTranslator({
		locale,
		messages: translations,
	});

	const benefits = [
		"Cursos exclusivos da organização",
		"Área de membros personalizada",
		"Suporte direto da equipe",
		"Comunidade de membros ativos"
	];

	return (
		<Wrapper>
			<Heading className="text-2xl font-bold text-center mb-6">
				{t("mail.organizationInvitation.welcome")}
			</Heading>

			<Text className="text-lg mb-4">
				{t("mail.common.greeting")},
			</Text>

			<Text className="mb-4">
				{inviterName
					? t("mail.organizationInvitation.invitedBy", { inviterName, organizationName })
					: t("mail.organizationInvitation.body", { organizationName })
				}
			</Text>

			<Text className="font-semibold mb-2">
				{t("mail.organizationInvitation.benefits")}
			</Text>

			<ul className="mb-6 pl-4">
				{benefits.map((benefit: string, index: number) => (
					<li key={index} className="mb-1 text-sm">• {benefit}</li>
				))}
			</ul>

			<Text className="mb-4">
				{t("mail.organizationInvitation.getStarted")}
			</Text>

			<PrimaryButton href={url}>
				{t("mail.organizationInvitation.join")}
			</PrimaryButton>

			<Text className="mt-4 text-sm text-muted-foreground">
				{t("mail.common.openLinkInBrowser")}
				<br />
				<Link href={url} className="break-all">{url}</Link>
			</Text>

			<Hr className="my-6" />

			<Text className="text-sm">
				{t("mail.organizationInvitation.needHelp")} <Link href={`mailto:${t("mail.organizationInvitation.supportEmail")}`}>{t("mail.organizationInvitation.supportEmail")}</Link>
			</Text>

			<Text className="text-sm text-muted-foreground mt-6">
				{t("mail.common.regards")}
				<br />
				{t("mail.common.team")}
			</Text>
		</Wrapper>
	);
}

OrganizationInvitation.PreviewProps = {
	locale: defaultLocale,
	translations: defaultTranslations,
	url: "#",
	organizationName: "Cakto Academy",
	inviterName: "João Silva",
};

export default OrganizationInvitation;
