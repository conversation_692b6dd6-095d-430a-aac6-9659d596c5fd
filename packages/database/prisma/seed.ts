import { PrismaClient } from "@prisma/client";
import { auth } from "@repo/auth";
import { createUser, createUserAccount, getUserByEmail } from "@repo/database";

const prisma = new PrismaClient();

// Available local images for cycling
const availableImages = [
  "/images/cards/card1.jpg",
  "/images/cards/card2.jpg",
  "/images/cards/card3.jpg",
  "/images/cards/card4.jpg",
  "/images/cards/card5.jpg",
  "/images/cards/card6.png",
  "/images/cards/card7.png",
  "/images/cards/card8.png",
  "/images/cards/card9.png",
  "/images/cards/card10.png"
];

// Image cycling logic
let imageIndex = 0;
const getNextImage = (): string => {
  const image = availableImages[imageIndex];
  imageIndex = (imageIndex + 1) % availableImages.length;
  return image;
};

async function main() {
  console.log("🧹 Limpando banco de dados...");

  // Clean up existing data in correct order (respecting foreign key constraints)
  await prisma.userPurchase.deleteMany();
  await prisma.vitrineSectionCourse.deleteMany();
  await prisma.vitrineSection.deleteMany();
  await prisma.vitrineView.deleteMany();
  await prisma.vitrine.deleteMany();

  await prisma.lessonCommentReplies.deleteMany();
  await prisma.lessonComments.deleteMany();
  await prisma.userWatchedLessons.deleteMany();
  await prisma.lessonFiles.deleteMany();
  await prisma.lessons.deleteMany();
  await prisma.courseModules.deleteMany();
  await prisma.modules.deleteMany();
  await prisma.courseBannerButton.deleteMany();
  await prisma.courseBanner.deleteMany();
  await prisma.userCourses.deleteMany();
  await prisma.courses.deleteMany();

  await prisma.userSystemColors.deleteMany();
  await prisma.session.deleteMany();
  await prisma.account.deleteMany();
  await prisma.user.deleteMany();

  await prisma.memberAreaSettings.deleteMany();
  await prisma.member.deleteMany();
  await prisma.invitation.deleteMany();
  await prisma.organization.deleteMany();

  console.log("✨ Criando usuários com Better Auth...");

  // Get Better Auth context for password hashing
  const authContext = await auth.$context;

  // Create users with Better Auth
  const users = [
    {
      email: "<EMAIL>",
      name: "Administrador Principal",
      role: "admin",
      password: "admin123"
    },
    {
      email: "<EMAIL>",
      name: "Caio Martins",
      role: "admin",
      password: "admin123"
    },
    {
      email: "<EMAIL>",
      name: "Ana Silva",
      role: "admin",
      password: "admin123"
    },
    {
      email: "<EMAIL>",
      name: "Ismael Costa",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "João Pedro Oliveira",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "Maria Fernanda Costa",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "Lucas Rodrigues",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "Beatriz Almeida",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "Rafael Mendes",
      role: "user",
      password: "user123"
    },
    {
      email: "<EMAIL>",
      name: "Camila Souza",
      role: "user",
      password: "user123"
    }
  ];

  const createdUsers = [];

  for (const userData of users) {
    try {
      // Create hash da senha usando Better Auth
      const hashedPassword = await authContext.password.hash(userData.password);

      // Criar usuário
      const user = await createUser({
        email: userData.email,
        name: userData.name,
        role: userData.role as "user" | "admin",
        emailVerified: true,
        onboardingComplete: true,
      });

      if (!user) {
        console.log(`❌ Falha ao criar usuário: ${userData.email}`);
        continue;
      }

      // Criar conta com senha
      await createUserAccount({
        userId: user.id,
        providerId: "credential",
        accountId: user.id, // Better Auth usa ID do usuário como accountId
        hashedPassword,
      });

      createdUsers.push(user);
      console.log(`✅ Usuário criado: ${userData.email} (${userData.role})`);

    } catch (error) {
      console.log(`❌ Erro ao criar usuário ${userData.email}:`, error);
    }
  }

  const [superAdmin, admin1, admin2, ismaelUser, ...otherUsers] = createdUsers;

  console.log("🏢 Criando organizações...");

  // Create organizations
  const organizations = await Promise.all([
    prisma.organization.create({
      data: {
        name: "Cakto Academy",
        slug: "cakto-academy",
        logo: "https://ui-avatars.com/api/?name=Cakto+Academy&background=4f46e5&color=fff",
        createdAt: new Date()
      }
    }),
    prisma.organization.create({
      data: {
        name: "Marketing Team",
        slug: "marketing-team",
        logo: "https://ui-avatars.com/api/?name=Marketing+Team&background=dc2626&color=fff",
        createdAt: new Date()
      }
    }),
    prisma.organization.create({
      data: {
        name: "TechCorp Premium",
        slug: "techcorp-premium",
        logo: "https://ui-avatars.com/api/?name=TechCorp+Premium&background=dc2626&color=fff",
        createdAt: new Date()
      }
    }),
    prisma.organization.create({
      data: {
        name: "Afiliados Network",
        slug: "afiliados-network",
        logo: "https://ui-avatars.com/api/?name=Afiliados+Network&background=f59e0b&color=fff",
        createdAt: new Date()
      }
    }),
    prisma.organization.create({
      data: {
        name: "EduTech Institute",
        slug: "edutech-institute",
        logo: "https://ui-avatars.com/api/?name=EduTech+Institute&background=8b5cf6&color=fff",
        createdAt: new Date()
      }
    }),
    prisma.organization.create({
      data: {
        name: "InnovateLab",
        slug: "innovatelab",
        logo: "https://ui-avatars.com/api/?name=InnovateLab&background=10b981&color=fff",
        createdAt: new Date()
      }
    })
  ]);

  console.log("👥 Criando membros das organizações...");

  // Create members for each organization
  for (let i = 0; i < organizations.length; i++) {
    const organization = organizations[i];
    const userIndex = i % createdUsers.length;
    const user = createdUsers[userIndex];

    // Create owner
    await prisma.member.create({
      data: {
        organizationId: organization.id,
        userId: user.id,
        role: "owner",
        createdAt: new Date()
      }
    });

    // Create additional members for some organizations
    if (i < 3) {
      const additionalUser = createdUsers[(i + 1) % createdUsers.length];
      await prisma.member.create({
        data: {
          organizationId: organization.id,
          userId: additionalUser.id,
          role: "admin",
          createdAt: new Date()
        }
      });
    }
  }

  console.log("📚 Criando cursos de Marketing Digital...");

  // Create comprehensive courses with different creators
  const courses = await Promise.all([
    prisma.courses.create({
      data: {
        name: "Marketing Digital Completo",
        logo: getNextImage(),
        organizationId: organizations[0].id,
        createdBy: superAdmin.id
      }
    }),
    prisma.courses.create({
      data: {
        name: "Tráfego Pago Masterclass",
        logo: getNextImage(),
        organizationId: organizations[0].id,
        createdBy: superAdmin.id
      }
    }),
    prisma.courses.create({
      data: {
        name: "Copywriting Persuasivo",
        logo: getNextImage(),
        organizationId: organizations[1].id,
        createdBy: admin1.id
      }
    }),
    prisma.courses.create({
      data: {
        name: "Gestão de Redes Sociais",
        logo: getNextImage(),
        organizationId: organizations[1].id,
        createdBy: admin1.id
      }
    }),
    prisma.courses.create({
      data: {
        name: "Email Marketing Avançado",
        logo: getNextImage(),
        organizationId: organizations[0].id,
        createdBy: superAdmin.id
      }
    }),
    prisma.courses.create({
      data: {
        name: "SEO e Marketing de Conteúdo",
        logo: getNextImage(),
        organizationId: organizations[0].id,
        createdBy: superAdmin.id
      }
    }),
    prisma.courses.create({
      data: {
        name: "Automação de Marketing",
        logo: getNextImage(),
        organizationId: organizations[0].id,
        createdBy: superAdmin.id
      }
    }),
    prisma.courses.create({
      data: {
        name: "Vendas Online Avançadas",
        logo: getNextImage(),
        organizationId: organizations[0].id,
        createdBy: superAdmin.id
      }
    }),
    prisma.courses.create({
      data: {
        name: "Analytics e Métricas",
        logo: getNextImage(),
        organizationId: organizations[2].id,
        createdBy: admin2.id
      }
    }),
    prisma.courses.create({
      data: {
        name: "Design para Marketing",
        logo: getNextImage(),
        organizationId: organizations[2].id,
        createdBy: admin2.id
      }
    }),
    prisma.courses.create({
      data: {
        name: "Inbound Marketing",
        logo: getNextImage(),
        organizationId: organizations[1].id,
        createdBy: admin1.id
      }
    }),
    prisma.courses.create({
      data: {
        name: "Marketing de Influência",
        logo: getNextImage(),
        organizationId: organizations[1].id,
        createdBy: admin1.id
      }
    })
  ]);

  console.log("🎬 Criando banners dos cursos...");

  // Create course banners for all courses
  const courseBanners = await Promise.all(
    courses.map((course, index) =>
      prisma.courseBanner.create({
        data: {
          courseId: course.id,
          title: `Transforme Sua Carreira com ${course.name}`,
          description: `Domine ${course.name.toLowerCase()} e transforme sua carreira com este curso completo que vai te ensinar desde os fundamentos até as estratégias mais avançadas do mercado.`,
          image: "/images/banner1.jpg",
          position: 1
        }
      })
    )
  );

  // Create banner buttons for all banners
  for (const banner of courseBanners) {
    await Promise.all([
      prisma.courseBannerButton.create({
        data: {
          bannerId: banner.id,
          title: "Assistir Agora",
          link: "/play",
          color: "#4f46e5",
          userId: superAdmin.id
        }
      }),
      prisma.courseBannerButton.create({
        data: {
          bannerId: banner.id,
          title: "Mais Informações",
          link: "/info",
          color: "#6b7280",
          userId: superAdmin.id
        }
      })
    ]);
  }

  console.log("📖 Criando módulos e aulas para todos os cursos...");

  // Bunny.net video URLs - usando vídeos de demonstração
  const bunnyVideoUrls = [
    'https://iframe.mediadelivery.net/embed/759/1a1a1a1a-1a1a-1a1a-1a1a-1a1a1a1a1a1a',
    'https://iframe.mediadelivery.net/embed/759/2b2b2b2b-2b2b-2b2b-2b2b-2b2b2b2b2b2b',
    'https://iframe.mediadelivery.net/embed/759/3c3c3c3c-3c3c-3c3c-3c3c-3c3c3c3c3c3c',
    'https://iframe.mediadelivery.net/embed/759/4d4d4d4d-4d4d-4d4d-4d4d-4d4d4d4d4d4d',
    'https://iframe.mediadelivery.net/embed/759/5e5e5e5e-5e5e-5e5e-5e5e-5e5e5e5e5e5e',
    'https://iframe.mediadelivery.net/embed/759/6f6f6f6f-6f6f-6f6f-6f6f-6f6f6f6f6f6f',
    'https://iframe.mediadelivery.net/embed/759/7g7g7g7g-7g7g-7g7g-7g7g-7g7g7g7g7g7g',
    'https://iframe.mediadelivery.net/embed/759/8h8h8h8h-8h8h-8h8h-8h8h-8h8h8h8h8h8h'
  ];

  // Create comprehensive modules and lessons for all courses
  for (const course of courses) {
    const moduleCount = 6; // 6 modules per course for comprehensive content

    for (let i = 0; i < moduleCount; i++) {
      const module = await prisma.modules.create({
        data: {
          name: `Módulo ${i + 1}: ${getMarketingModuleTitle(i, course.name)}`,
          position: i + 1
        }
      });

      // Create exactly 8 lessons for this module
      for (let j = 0; j < 8; j++) {
        await prisma.lessons.create({
          data: {
            moduleId: module.id,
            name: `Aula ${j + 1}: ${getMarketingLessonTitle(j, module.name)}`,
            description: `Nesta aula você vai aprender ${getMarketingLessonDescription(j, course.name)}`,
            videoUrl: bunnyVideoUrls[j % bunnyVideoUrls.length],
            duration: `${Math.floor(Math.random() * 30) + 15}min`, // 15-45 minutes
            position: j + 1
          }
        });
      }

      // Link module to course
      await prisma.courseModules.create({
        data: {
          courseId: course.id,
          moduleId: module.id
        }
      });
    }
  }

  console.log("👤 Adicionando todos os cursos para Ismael Costa (usuário de teste)...");

  // Add all courses for Ismael Costa (usuário de teste)
  await Promise.all(
    courses.map(async (course) => {
      return prisma.userCourses.create({
        data: {
          userId: ismaelUser.id,
          courseId: course.id
        }
      });
    })
  );

  // Add some courses for other users as well
  for (let i = 0; i < 3; i++) {
    const user = otherUsers[i];
    const userCourses = courses.slice(i * 3, (i + 1) * 3); // 3 courses per user

    await Promise.all(
      userCourses.map(async (course) => {
        return prisma.userCourses.create({
          data: {
            userId: user.id,
            courseId: course.id
          }
        });
      })
    );
  }

  console.log("🏪 Criando vitrines com múltiplas sessões...");

  // Create comprehensive showcase vitrines
  const vitrines = await Promise.all([
    prisma.vitrine.create({
      data: {
        title: "Trilha Marketing Digital Completa",
        description: "Cursos essenciais para dominar o marketing digital do zero ao avançado",
        status: "PUBLISHED",
        visibility: "PUBLIC",
        organizationId: organizations[0].id,
        createdBy: superAdmin.id
      }
    }),
    prisma.vitrine.create({
      data: {
        title: "Trilha Vendas e Conversão",
        description: "Estratégias avançadas de vendas e conversão para resultados rápidos",
        status: "PUBLISHED",
        visibility: "PUBLIC",
        organizationId: organizations[0].id,
        createdBy: superAdmin.id
      }
    }),
    prisma.vitrine.create({
      data: {
        title: "Trilha Desenvolvimento Web",
        description: "Do zero ao profissional em desenvolvimento web e programação",
        status: "PUBLISHED",
        visibility: "PUBLIC",
        organizationId: organizations[2].id,
        createdBy: admin2.id
      }
    }),
    prisma.vitrine.create({
      data: {
        title: "Trilha Design e Criatividade",
        description: "Cursos de design, criatividade e ferramentas visuais",
        status: "PUBLISHED",
        visibility: "PUBLIC",
        organizationId: organizations[1].id,
        createdBy: admin1.id
      }
    }),
    prisma.vitrine.create({
      data: {
        title: "Trilha Analytics e Dados",
        description: "Análise de dados, métricas e tomada de decisões baseada em dados",
        status: "PUBLISHED",
        visibility: "PUBLIC",
        organizationId: organizations[2].id,
        createdBy: admin2.id
      }
    })
  ]);

  // Create multiple sections for each vitrine with 8 courses each
  for (let i = 0; i < vitrines.length; i++) {
    const vitrine = vitrines[i];
    const sectionCount = 3; // 3 sections per vitrine

    for (let sectionIndex = 0; sectionIndex < sectionCount; sectionIndex++) {
      // Create a section for this vitrine
      const section = await prisma.vitrineSection.create({
        data: {
          vitrineId: vitrine.id,
          title: `Seção ${sectionIndex + 1} - ${getSectionTitle(sectionIndex, vitrine.title)}`,
          subtitle: `Cursos especializados de ${getSectionSubtitle(sectionIndex, vitrine.title)}`,
          description: `Os melhores cursos de ${getSectionDescription(sectionIndex, vitrine.title)} para você dominar o mercado.`,
          position: sectionIndex + 1,
          isLocked: sectionIndex === 1, // Second section requires purchase
          requiresPurchase: sectionIndex === 1,
          accessType: sectionIndex === 1 ? "PAID" : "FREE",
          visibility: "PUBLIC",
          price: sectionIndex === 1 ? 97.0 : null,
          originalPrice: sectionIndex === 1 ? 197.0 : null
        }
      });

      // Add 8 courses to this section
      const startIndex = (i * sectionCount + sectionIndex) * 8;
      for (let j = 0; j < 8; j++) {
        const courseIndex = (startIndex + j) % courses.length;
        await prisma.vitrineSectionCourse.create({
          data: {
            sectionId: section.id,
            courseId: courses[courseIndex].id,
            position: j + 1
          }
        });
      }

      console.log(
        `✅ Seção "${section.title}" criada com 8 cursos para vitrine "${vitrine.title}"`
      );
    }
  }

  console.log("🎨 Criando configurações de área de membros...");

  // Helper function to create member area settings
  const createMemberAreaSettings = (subdomain: string, data: any) => {
    return prisma.memberAreaSettings.upsert({
      where: { subdomain },
      update: data,
      create: { ...data, subdomain }
    });
  };

  // Create comprehensive member area settings for all organizations
  const memberAreaSettings = await Promise.all([
    // Área de membros principal da Cakto Academy
    createMemberAreaSettings("academy", {
      organizationId: organizations[0].id,
      memberAreaName: "Cakto Academy - Área de Membros",
      primaryColor: "#4f46e5",
      logoUrl:
        "https://ui-avatars.com/api/?name=Cakto+Academy&background=4f46e5&color=fff",
      commentsEnabled: true,
      supportEmail: "<EMAIL>",
      menuItems: [
        {
          title: "Dashboard",
          url: "/dashboard",
          icon: "dashboard",
          position: 1
        },
        {
          title: "Meus Cursos",
          url: "/cursos",
          icon: "book",
          position: 2
        },
        {
          title: "Comunidade",
          url: "/comunidade",
          icon: "users",
          position: 3
        },
        {
          title: "Suporte",
          url: "/suporte",
          icon: "help",
          position: 4
        }
      ],
      footer: {
        enabled: true,
        text: "© 2024 Cakto Academy. Todos os direitos reservados.",
        links: [
          {
            title: "Política de Privacidade",
            url: "/privacidade"
          },
          {
            title: "Termos de Uso",
            url: "/termos"
          },
          {
            title: "Contato",
            url: "/contato"
          }
        ],
        socialLinks: [
          {
            platform: "instagram",
            url: "https://instagram.com/caktoacademy"
          },
          {
            platform: "youtube",
            url: "https://youtube.com/caktoacademy"
          },
          {
            platform: "discord",
            url: "https://discord.gg/caktoacademy"
          }
        ]
      }
    }),

    // Área de membros premium da Cakto Academy
    createMemberAreaSettings("premium", {
      organizationId: organizations[0].id,
      memberAreaName: "Cakto Academy Premium",
      primaryColor: "#059669",
      logoUrl:
        "https://ui-avatars.com/api/?name=Cakto+Premium&background=059669&color=fff",
      commentsEnabled: true,
      supportEmail: "<EMAIL>",
      menuItems: [
        {
          title: "Dashboard Premium",
          url: "/dashboard",
          icon: "dashboard",
          position: 1
        },
        {
          title: "Cursos Exclusivos",
          url: "/cursos-premium",
          icon: "star",
          position: 2
        },
        {
          title: "Mentorias",
          url: "/mentorias",
          icon: "user-check",
          position: 3
        },
        {
          title: "Comunidade VIP",
          url: "/comunidade-vip",
          icon: "crown",
          position: 4
        },
        {
          title: "Suporte Prioritário",
          url: "/suporte-premium",
          icon: "headphones",
          position: 5
        }
      ],
      footer: {
        enabled: true,
        text: "© 2024 Cakto Academy Premium. Experiência exclusiva para membros VIP.",
        links: [
          {
            title: "Política de Privacidade",
            url: "/privacidade"
          },
          {
            title: "Termos Premium",
            url: "/termos-premium"
          },
          {
            title: "Suporte VIP",
            url: "/suporte-vip"
          }
        ],
        socialLinks: [
          {
            platform: "instagram",
            url: "https://instagram.com/caktoacademy"
          },
          {
            platform: "discord",
            url: "https://discord.gg/caktoacademy-vip"
          }
        ]
      }
    }),

    // Área de membros do Marketing Team
    createMemberAreaSettings("marketing", {
      organizationId: organizations[1].id,
      memberAreaName: "Marketing Team Hub",
      primaryColor: "#dc2626",
      logoUrl:
        "https://ui-avatars.com/api/?name=Marketing+Hub&background=dc2626&color=fff",
      commentsEnabled: true,
      supportEmail: "<EMAIL>",
      menuItems: [
        {
          title: "Dashboard",
          url: "/dashboard",
          icon: "dashboard",
          position: 1
        },
        {
          title: "Campanhas",
          url: "/campanhas",
          icon: "megaphone",
          position: 2
        },
        {
          title: "Analytics",
          url: "/analytics",
          icon: "chart",
          position: 3
        },
        {
          title: "Recursos",
          url: "/recursos",
          icon: "folder",
          position: 4
        }
      ],
      footer: {
        enabled: true,
        text: "© 2024 Marketing Team. Estratégias que convertem.",
        links: [
          {
            title: "Política de Privacidade",
            url: "/privacidade"
          },
          {
            title: "Termos de Uso",
            url: "/termos"
          }
        ],
        socialLinks: [
          {
            platform: "instagram",
            url: "https://instagram.com/caktomarketing"
          },
          {
            platform: "linkedin",
            url: "https://linkedin.com/company/cakto-marketing"
          }
        ]
      }
    }),

    // Área de membros da TechCorp Premium
    createMemberAreaSettings("techcorp", {
      organizationId: organizations[2].id,
      memberAreaName: "TechCorp Enterprise Portal",
      primaryColor: "#dc2626",
      logoUrl:
        "https://ui-avatars.com/api/?name=TechCorp+Enterprise&background=dc2626&color=fff",
      commentsEnabled: true,
      supportEmail: "<EMAIL>",
      menuItems: [
        {
          title: "Dashboard",
          url: "/dashboard",
          icon: "dashboard",
          position: 1
        },
        {
          title: "Treinamentos",
          url: "/treinamentos",
          icon: "graduation-cap",
          position: 2
        },
        {
          title: "Certificações",
          url: "/certificacoes",
          icon: "certificate",
          position: 3
        },
        {
          title: "Projetos",
          url: "/projetos",
          icon: "briefcase",
          position: 4
        },
        {
          title: "Suporte Enterprise",
          url: "/suporte",
          icon: "headphones",
          position: 5
        }
      ],
      footer: {
        enabled: true,
        text: "© 2024 TechCorp Premium. Soluções corporativas de excelência.",
        links: [
          {
            title: "Política de Privacidade",
            url: "/privacidade"
          },
          {
            title: "Termos Enterprise",
            url: "/termos-enterprise"
          },
          {
            title: "SLA",
            url: "/sla"
          }
        ],
        socialLinks: [
          {
            platform: "linkedin",
            url: "https://linkedin.com/company/techcorp"
          },
          {
            platform: "github",
            url: "https://github.com/techcorp"
          }
        ]
      }
    }),

    // Área de membros da Afiliados Network
    createMemberAreaSettings("afiliados", {
      organizationId: organizations[3].id,
      memberAreaName: "Afiliados Network Hub",
      primaryColor: "#f59e0b",
      logoUrl:
        "https://ui-avatars.com/api/?name=Afiliados+Network&background=f59e0b&color=fff",
      commentsEnabled: false,
      supportEmail: "<EMAIL>",
      menuItems: [
        {
          title: "Dashboard",
          url: "/dashboard",
          icon: "dashboard",
          position: 1
        },
        {
          title: "Comissões",
          url: "/comissoes",
          icon: "dollar-sign",
          position: 2
        },
        {
          title: "Materiais",
          url: "/materiais",
          icon: "download",
          position: 3
        },
        {
          title: "Relatórios",
          url: "/relatorios",
          icon: "bar-chart",
          position: 4
        },
        {
          title: "Ranking",
          url: "/ranking",
          icon: "trophy",
          position: 5
        }
      ],
      footer: {
        enabled: true,
        text: "© 2024 Afiliados Network. Ganhe comissões promovendo produtos digitais.",
        links: [
          {
            title: "Termos de Afiliação",
            url: "/termos-afiliacao"
          },
          {
            title: "Política de Comissões",
            url: "/politica-comissoes"
          },
          {
            title: "FAQ",
            url: "/faq"
          }
        ],
        socialLinks: [
          {
            platform: "telegram",
            url: "https://t.me/afiliadosnetwork"
          },
          {
            platform: "whatsapp",
            url: "https://wa.me/5511999999999"
          }
        ]
      }
    }),

    // Área de membros da EduTech Institute
    createMemberAreaSettings("edutech", {
      organizationId: organizations[4].id,
      memberAreaName: "EduTech Student Portal",
      primaryColor: "#8b5cf6",
      logoUrl:
        "https://ui-avatars.com/api/?name=EduTech+Portal&background=8b5cf6&color=fff",
      commentsEnabled: true,
      supportEmail: "<EMAIL>",
      menuItems: [
        {
          title: "Dashboard",
          url: "/dashboard",
          icon: "dashboard",
          position: 1
        },
        {
          title: "Disciplinas",
          url: "/disciplinas",
          icon: "book-open",
          position: 2
        },
        {
          title: "Biblioteca",
          url: "/biblioteca",
          icon: "library",
          position: 3
        },
        {
          title: "Notas",
          url: "/notas",
          icon: "file-text",
          position: 4
        },
        {
          title: "Calendário",
          url: "/calendario",
          icon: "calendar",
          position: 5
        },
        {
          title: "Suporte Acadêmico",
          url: "/suporte",
          icon: "help-circle",
          position: 6
        }
      ],
      footer: {
        enabled: true,
        text: "© 2024 EduTech Institute. Educação tecnológica de qualidade.",
        links: [
          {
            title: "Política de Privacidade",
            url: "/privacidade"
          },
          {
            title: "Regulamento Acadêmico",
            url: "/regulamento"
          },
          {
            title: "Ouvidoria",
            url: "/ouvidoria"
          }
        ],
        socialLinks: [
          {
            platform: "instagram",
            url: "https://instagram.com/edutech.institute"
          },
          {
            platform: "youtube",
            url: "https://youtube.com/edutech"
          },
          {
            platform: "linkedin",
            url: "https://linkedin.com/school/edutech-institute"
          }
        ]
      }
    }),

    // Área de membros da InnovateLab
    createMemberAreaSettings("innovate", {
      organizationId: organizations[5].id,
      memberAreaName: "InnovateLab Startup Hub",
      primaryColor: "#10b981",
      logoUrl:
        "https://ui-avatars.com/api/?name=InnovateLab+Hub&background=10b981&color=fff",
      commentsEnabled: true,
      supportEmail: "<EMAIL>",
      menuItems: [
        {
          title: "Dashboard",
          url: "/dashboard",
          icon: "dashboard",
          position: 1
        },
        {
          title: "Projetos",
          url: "/projetos",
          icon: "lightbulb",
          position: 2
        },
        {
          title: "Mentoria",
          url: "/mentoria",
          icon: "user-check",
          position: 3
        },
        {
          title: "Pitch Deck",
          url: "/pitch",
          icon: "presentation",
          position: 4
        },
        {
          title: "Networking",
          url: "/networking",
          icon: "users",
          position: 5
        }
      ],
      footer: {
        enabled: true,
        text: "© 2024 InnovateLab. Inovação que transforma o futuro.",
        links: [
          {
            title: "Política de Privacidade",
            url: "/privacidade"
          },
          {
            title: "Termos de Inovação",
            url: "/termos-inovacao"
          },
          {
            title: "Parcerias",
            url: "/parcerias"
          }
        ],
        socialLinks: [
          {
            platform: "twitter",
            url: "https://twitter.com/innovatelab"
          },
          {
            platform: "linkedin",
            url: "https://linkedin.com/company/innovatelab"
          },
          {
            platform: "github",
            url: "https://github.com/innovatelab"
          }
        ]
      }
    })
  ]);

  console.log("🔐 Criando sessões com organizações padrão...");

  // Criar sessões para todos os usuários com suas organizações padrão
  for (const user of createdUsers) {
    // Buscar a primeira organização do usuário
    const userMembership = await prisma.member.findFirst({
      where: { userId: user.id },
      include: {
        organization: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        createdAt: "asc"
      }
    });

    if (userMembership) {
      // Criar sessão com organização padrão
      await prisma.session.create({
        data: {
          userId: user.id,
          token: `session_${user.id}_${Date.now()}`,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 dias
          defaultOrganizationId: userMembership.organization.id,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });

      console.log(`✓ Sessão criada para ${user.name} com organização padrão: ${userMembership.organization.name}`);
    }
  }

  console.log("✅ Seed concluído com sucesso!");
  console.log("📊 Resumo dos dados criados:");
  console.log(
    `👥 ${createdUsers.length} usuários (${createdUsers.filter(u => u.role === 'admin').length} admins + ${createdUsers.filter(u => u.role === 'user').length} usuários)`
  );
  console.log(`🏢 ${organizations.length} organizações`);
  console.log(
    `👨‍💼 Membros organizacionais: ${organizations.length + 3} (incluindo owners, admins e members)`
  );
  console.log(`📚 ${courses.length} cursos de Marketing Digital`);
  console.log(`📖 ${courses.length * 6} módulos com 8 aulas cada (${courses.length * 6 * 8} aulas total)`);
  console.log(`🎬 ${courseBanners.length} banners de cursos`);
  console.log(`🏪 ${vitrines.length} vitrines com ${vitrines.length * 3} sessões (8 cursos cada)`);
  console.log(`🎨 ${memberAreaSettings.length} áreas de membros configuradas`);
  console.log(`🔐 ${createdUsers.length} sessões criadas com organizações padrão`);

  console.log("\n🔐 Contas de teste:");
  console.log("Super Admin: <EMAIL> / admin123");
  console.log("Admin: <EMAIL> / admin123");
  console.log("Ismael Costa (acesso a TODOS os cursos): <EMAIL> / user123");
  console.log("Usuário: <EMAIL> / user123");

  console.log("\n🌐 Subdomínios configurados:");
  console.log("academy.caktomembers.com - Cakto Academy (Principal)");
  console.log("premium.caktomembers.com - Cakto Academy Premium");
  console.log("marketing.caktomembers.com - Marketing Team Hub");
  console.log("techcorp.caktomembers.com - TechCorp Enterprise Portal");
  console.log("afiliados.caktomembers.com - Afiliados Network Hub");
  console.log("edutech.caktomembers.com - EduTech Student Portal");
  console.log("innovate.caktomembers.com - InnovateLab Startup Hub");
}

// Helper functions for generating realistic marketing content
function getMarketingModuleTitle(index: number, courseTitle: string): string {
  const titles = [
    "Fundamentos do Marketing Digital",
    "Estratégias de Conteúdo e SEO",
    "Tráfego Pago e Anúncios",
    "Automação e Ferramentas",
    "Análise de Dados e Otimização",
    "Estratégias Avançadas e Case Studies"
  ];
  return titles[index] || `Módulo ${index + 1}`;
}

function getMarketingLessonTitle(index: number, moduleTitle: string): string {
  const titles = [
    "Introdução e Visão Geral",
    "Conceitos Fundamentais",
    "Planejamento Estratégico",
    "Implementação Prática",
    "Ferramentas Essenciais",
    "Análise de Resultados",
    "Otimização e Melhorias",
    "Próximos Passos e Ação"
  ];
  return titles[index] || `Aula ${index + 1}`;
}

function getMarketingLessonDescription(index: number, courseName: string): string {
  const descriptions = [
    "os fundamentos essenciais do marketing digital",
    "os conceitos básicos necessários para o sucesso",
    "como planejar suas estratégias de marketing",
    "como implementar na prática suas campanhas",
    "as ferramentas mais importantes do mercado",
    "como analisar e interpretar seus resultados",
    "técnicas de otimização para maximizar resultados",
    "como dar continuidade e escalar seus esforços"
  ];
  return descriptions[index] || "conceitos importantes de marketing digital";
}

function getSectionTitle(index: number, vitrineTitle: string): string {
  const sections = [
    "Fundamentos",
    "Estratégias Intermediárias",
    "Técnicas Avançadas"
  ];
  return sections[index] || `Seção ${index + 1}`;
}

function getSectionSubtitle(index: number, vitrineTitle: string): string {
  const subtitles = [
    "conceitos básicos e fundamentos",
    "estratégias intermediárias e práticas",
    "técnicas avançadas e otimização"
  ];
  return subtitles[index] || "conteúdo especializado";
}

function getSectionDescription(index: number, vitrineTitle: string): string {
  const descriptions = [
    "fundamentos essenciais",
    "estratégias intermediárias",
    "técnicas avançadas"
  ];
  return descriptions[index] || "conteúdo especializado";
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
