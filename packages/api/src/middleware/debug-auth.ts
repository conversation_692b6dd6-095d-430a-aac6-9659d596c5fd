import { type Session, auth } from "@repo/auth";
import { createMiddleware } from "hono/factory";

export const debugAuthMiddleware = createMiddleware<{
	Variables: {
		session: Session["session"];
		user: Session["user"];
	};
}>(async (c, next) => {
	console.log("🔍 Debug Auth Middleware - Starting");
	console.log("🔍 Headers:", Object.fromEntries(c.req.raw.headers.entries()));
	console.log("🔍 URL:", c.req.url);
	console.log("🔍 Method:", c.req.method);
	
	try {
		const session = await auth.api.getSession({
			headers: c.req.raw.headers,
		});

		console.log("🔍 Session result:", session ? "FOUND" : "NOT FOUND");
		if (session) {
			console.log("🔍 User:", session.user?.email, session.user?.id);
			console.log("🔍 Session ID:", session.session?.id);
		}

		if (!session) {
			console.log("❌ No session found - returning 401");
			return c.json({ error: "Unauthorized" }, 401);
		}

		c.set("session", session.session);
		c.set("user", session.user);

		console.log("✅ Auth middleware passed - proceeding to next");
		await next();
	} catch (error) {
		console.error("❌ Error in debug auth middleware:", error);
		return c.json({ error: "Authentication error" }, 500);
	}
});