import { Hono } from "hono";
import { describe<PERSON>oute } from "hono-openapi";
import { z } from "zod";
import { db } from "@repo/database";
import { logger } from "@repo/logs";

export const caktoProductsRouter = new Hono()
  .get(
    "/admin/cakto-products",
    describeRoute({
      tags: ["Admin - Cakto Integration"],
      summary: "Get Cakto products for course association",
      description: "Fetch products from Cakto backend that can be associated with courses",
      security: [{ cookieAuth: [] }],
      responses: {
        200: {
          description: "List of Cakto products",
          content: {
            "application/json": {
              schema: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    id: { type: "string" },
                    name: { type: "string" },
                    description: { type: "string" },
                    price: { type: "number" },
                    image: { type: "string" },
                    alreadyLinked: { type: "boolean" }
                  }
                }
              }
            }
          }
        },
        401: {
          description: "Unauthorized",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: { type: "string" }
                }
              }
            }
          }
        },
        500: {
          description: "Server error",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: { type: "string" }
                }
              }
            }
          }
        }
      }
    }),
    async (c) => {
      try {
        const session = c.get("session");

        if (!session?.user) {
          return c.json({ error: "Unauthorized" }, 401);
        }

        // Verificar se o usuário tem permissão de admin
        if (session.user.role !== "admin") {
          return c.json({ error: "Admin permission required" }, 403);
        }

        // Obter o token de acesso do cabeçalho de autorização
        const authHeader = c.req.header("authorization");
        const accessToken = authHeader?.startsWith("Bearer ")
          ? authHeader.substring(7)
          : null;

        if (!accessToken) {
          return c.json({ error: "Access token required" }, 401);
        }

        // URL da API do Cakto
        const caktoApiUrl = process.env.CAKTO_API_URL || "https://api.cakto.com.br";

        // Buscar produtos do usuário na Cakto
        const url = new URL("/api/products/", caktoApiUrl);
        url.searchParams.append("content_delivery", "cakto");
        url.searchParams.append("limit", "1000");

        const response = await fetch(url.toString(), {
          method: "GET",
          headers: {
            "Authorization": `Bearer ${accessToken}`,
            "Accept": "application/json"
          }
        });

        if (!response.ok) {
          const status = response.status;
          let errorMessage = "Error fetching products";

          if (status === 401) {
            errorMessage = "Invalid or expired token";
          } else if (status === 403) {
            errorMessage = "Unauthorized access";
          } else if (status >= 500) {
            errorMessage = "Cakto API server error";
          }

          logger.error(`Cakto products API failed: ${status} ${response.statusText}`);
          return c.json({ error: errorMessage }, status);
        }

        const productsData = await response.json();
        const products = productsData.results || productsData;

        // Verificar quais produtos já estão vinculados a cursos
        const linkedProducts = await db.courseProduct.findMany({
          where: {
            caktoProductId: {
              in: products.map((p: any) => p.id)
            }
          },
          select: { caktoProductId: true }
        });

        const linkedProductIds = new Set(linkedProducts.map(p => p.caktoProductId));

        // Formatar resposta
        const formattedProducts = products.map((product: any) => ({
          id: product.id,
          name: product.name,
          description: product.description || "",
          price: product.price || 0,
          image: product.image || "",
          alreadyLinked: linkedProductIds.has(product.id)
        }));

        return c.json(formattedProducts);
      } catch (error) {
        logger.error("Error fetching Cakto products", error);
        return c.json({ error: "Failed to fetch products" }, 500);
      }
    }
  )
  .post(
    "/admin/cakto-products/associate",
    describeRoute({
      tags: ["Admin - Cakto Integration"],
      summary: "Associate Cakto product with course",
      description: "Associate a Cakto product with a course in the members area",
      security: [{ cookieAuth: [] }],
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                courseId: { type: "string" },
                caktoProductId: { type: "string" },
                caktoProductName: { type: "string" }
              },
              required: ["courseId", "caktoProductId"]
            }
          }
        }
      },
      responses: {
        200: {
          description: "Association created successfully",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  data: {
                    type: "object",
                    properties: {
                      id: { type: "string" },
                      courseId: { type: "string" },
                      caktoProductId: { type: "string" },
                      caktoProductName: { type: "string" }
                    }
                  }
                }
              }
            }
          }
        },
        400: {
          description: "Bad request",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: { type: "string" }
                }
              }
            }
          }
        },
        401: {
          description: "Unauthorized",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: { type: "string" }
                }
              }
            }
          }
        },
        500: {
          description: "Server error",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: { type: "string" }
                }
              }
            }
          }
        }
      }
    }),
    async (c) => {
      try {
        const session = c.get("session");

        if (!session?.user) {
          return c.json({ error: "Unauthorized" }, 401);
        }

        // Verificar se o usuário tem permissão de admin
        if (session.user.role !== "admin") {
          return c.json({ error: "Admin permission required" }, 403);
        }

        // Validar o corpo da requisição
        const body = await c.req.json();
        const schema = z.object({
          courseId: z.string(),
          caktoProductId: z.string(),
          caktoProductName: z.string().optional()
        });

        const result = schema.safeParse(body);
        if (!result.success) {
          return c.json({ error: "Invalid request data", details: result.error.format() }, 400);
        }

        const { courseId, caktoProductId, caktoProductName } = result.data;

        // Verificar se o curso existe
        const course = await db.courses.findUnique({
          where: { id: courseId }
        });

        if (!course) {
          return c.json({ error: "Course not found" }, 404);
        }

        // Verificar se já existe uma associação
        const existingAssociation = await db.courseProduct.findFirst({
          where: {
            courseId,
            caktoProductId
          }
        });

        if (existingAssociation) {
          // Atualizar nome do produto se fornecido
          if (caktoProductName) {
            const updated = await db.courseProduct.update({
              where: { id: existingAssociation.id },
              data: { caktoProductName }
            });

            return c.json({
              success: true,
              data: updated,
              message: "Product association updated"
            });
          }

          return c.json({
            success: true,
            data: existingAssociation,
            message: "Product already associated with this course"
          });
        }

        // Criar nova associação
        const association = await db.courseProduct.create({
          data: {
            courseId,
            caktoProductId,
            caktoProductName: caktoProductName || null
          }
        });

        return c.json({
          success: true,
          data: association,
          message: "Product associated with course successfully"
        });
      } catch (error) {
        logger.error("Error associating Cakto product with course", error);
        return c.json({ error: "Failed to associate product with course" }, 500);
      }
    }
  );
