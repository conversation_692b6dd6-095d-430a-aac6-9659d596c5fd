import { db } from "@repo/database";
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'

const paramsSchema = z.object({
  lessonId: z.string(),
})

export const getLessonFiles = new Hono()
  .get('/lesson/:lessonId/files', validator('param', paramsSchema), async (c) => {
    try {
      const { lessonId } = c.req.valid('param')

      // Replace with Prisma query
      const files = await db.lessonFiles.findMany({
        where: { lessonId },
        orderBy: { id: 'asc' }
      });

      const formattedFiles = files.map(file => ({
        id: file.id.toString(),
        name: file.title,
        url: file.file,
        size: file.fileSize ? parseInt(file.fileSize) : null,
        type: 'application/octet-stream', // Determine type based on extension if possible
        lessonId,
      }));

      return c.json({ files })
    } catch (error) {
      return c.json({ error: 'Failed to fetch lesson files' }, 500)
    }
  })