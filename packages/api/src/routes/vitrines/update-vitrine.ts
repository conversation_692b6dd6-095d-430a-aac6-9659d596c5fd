import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const paramsSchema = z.object({
	id: z.string(),
});

const updateVitrineSchema = z.object({
	title: z.string().min(1, "Title is required").optional(),
	description: z.string().optional(),
	status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED"]).optional(),
	visibility: z.enum(["PUBLIC", "PRIVATE"]).optional(),
	bannerImage: z.string().url().optional(),
	isDefault: z.boolean().optional(),
	organizationId: z.string(),
});

export const updateVitrine = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	.put("/:id", validator("param", paramsSchema), validator("json", updateVitrineSchema), async (c) => {
		try {
			const user = c.get("user");
			const { id } = c.req.valid("param");
			const data = c.req.valid("json");
			const { organizationId, ...updateData } = data;

			// Verificar se o usuário tem permissão na organização
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			if (userMembership.role === "member") {
				return c.json({ error: "Only admins and owners can update vitrines" }, 403);
			}

			// Verificar se a vitrine existe e pertence à organização
			const existingVitrine = await db.vitrine.findFirst({
				where: {
					id,
					organizationId,
				},
			});

			if (!existingVitrine) {
				return c.json({ error: "Vitrine not found" }, 404);
			}

			// If this vitrine is being set as default, unset any existing default vitrines
			if (data.isDefault) {
				await db.vitrine.updateMany({
					where: {
						organizationId,
						isDefault: true,
						id: { not: id }, // Don't unset the current vitrine being updated
					},
					data: {
						isDefault: false,
					},
				});
			}

			// Atualizar vitrine
			const vitrine = await db.vitrine.update({
				where: { id },
				data: updateData,
				include: {
					sections: {
						orderBy: { position: "asc" },
						include: {
							courses: {
								orderBy: { position: "asc" },
								include: {
									course: {
										select: {
											id: true,
											name: true,
											logo: true,
										},
									},
								},
							},
						},
					},
					creator: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
				},
			});

			const response = {
				id: vitrine.id,
				title: vitrine.title,
				description: vitrine.description,
				status: vitrine.status,
				visibility: vitrine.visibility,
				bannerImage: vitrine.bannerImage,
				createdAt: vitrine.createdAt.toISOString(),
				updatedAt: vitrine.updatedAt.toISOString(),
				organizationId: vitrine.organizationId,
				createdBy: vitrine.createdBy,
				creator: vitrine.creator,
				sections: vitrine.sections.map((section) => ({
					id: section.id,
					title: section.title,
					subtitle: section.subtitle,
					description: section.description,
					position: section.position,
					isLocked: section.isLocked,
					requiresPurchase: section.requiresPurchase,
					checkoutUrl: section.checkoutUrl,
					webhookUrl: section.webhookUrl,
					price: section.price ? Number(section.price) : null,
					originalPrice: section.originalPrice ? Number(section.originalPrice) : null,
					accessType: section.accessType,
					visibility: section.visibility,
					courses: section.courses,
				})),
			};

			return c.json(response);
		} catch (error) {
			console.error("Error updating vitrine:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
