import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";

const querySchema = z.object({
	organizationId: z.string(),
	status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED"]).optional().default("PUBLISHED"),
	visibility: z.enum(["PUBLIC", "PRIVATE"]).optional().default("PUBLIC"),
});

export const getOrganizationVitrines = new Hono()
	.use(authMiddleware)
	.get("/organization", validator("query", querySchema), async (c) => {
		try {
			const user = c.get("user");
			const { organizationId, status, visibility } = c.req.valid("query");

			// Verificar se o usuário tem permissão na organização
			const userMembership = await db.member.findFirst({
				where: {
					userId: user.id,
					organizationId,
				},
			});

			if (!userMembership) {
				return c.json({ error: "User is not a member of this organization" }, 403);
			}

			const vitrines = await db.vitrine.findMany({
				where: {
					organizationId,
					status,
					visibility,
				},
				include: {
					sections: {
						orderBy: { position: "asc" },
						include: {
							courses: {
								select: {
									id: true,
									name: true,
									logo: true,
									community: true,
									description: true,
								},
							},
						},
					},
					organization: {
						select: {
							id: true,
							name: true,
							slug: true,
						},
					},
				},
				orderBy: { createdAt: "desc" },
			});

			const formattedVitrines = vitrines.map((vitrine) => ({
				id: vitrine.id,
				title: vitrine.title,
				description: vitrine.description,
				status: vitrine.status,
				visibility: vitrine.visibility,
				bannerImage: vitrine.bannerImage,
				createdAt: vitrine.createdAt.toISOString(),
				updatedAt: vitrine.updatedAt.toISOString(),
				organizationId: vitrine.organizationId,
				organization: vitrine.organization,
				sections: vitrine.sections.map((section) => ({
					id: section.id,
					title: section.title,
					subtitle: section.subtitle,
					description: section.description,
					position: section.position,
					isLocked: section.isLocked,
					requiresPurchase: section.requiresPurchase,
					checkoutUrl: section.checkoutUrl,
					price: section.price ? Number(section.price) : null,
					originalPrice: section.originalPrice ? Number(section.originalPrice) : null,
					accessType: section.accessType,
					visibility: section.visibility,
					courses: section.courses,
				})),
			}));

			return c.json({ data: formattedVitrines });
		} catch (error) {
			console.error("Error fetching organization vitrines:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});