import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";

const querySchema = z.object({
  organizationId: z.string().optional(),
  organizationSlug: z.string().optional(),
  status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED"]).optional().default("PUBLISHED"),
  visibility: z.enum(["PUBLIC", "PRIVATE"]).optional().default("PUBLIC"),
});

export const getPublicVitrines = new Hono().get(
	"/public",
	validator("query", querySchema),
	async (c) => {
		try {
			// Inside the handler:
			const { organizationId, organizationSlug, status, visibility } = c.req.valid("query");
			
			let whereClause = {
			  status,
			  visibility,
			};
			
			if (organizationId) {
			  whereClause = { ...whereClause, organizationId };
			} else if (organizationSlug) {
			  whereClause = { ...whereClause, organization: { slug: organizationSlug } };
			}
			
			const vitrines = await db.vitrine.findMany({
			  where: whereClause,
			  include: {
			    sections: {
			      orderBy: { position: "asc" },
			      include: {
			        courses: {
			          orderBy: { position: "asc" },
			          include: {
			            course: {
			              select: {
			                id: true,
			                name: true,
			                logo: true,
			                community: true,
			                link: true,
			                createdAt: true,
			                updatedAt: true,
			              },
			            },
			          },
			        },
			      },
			    },
			    organization: {
			      select: {
			        id: true,
			        name: true,
			        slug: true,
			        logo: true,
			      },
			    },
			  },
			  orderBy: { createdAt: "desc" },
			});

			const formattedVitrines = vitrines.map((vitrine) => ({
				id: vitrine.id,
				title: vitrine.title,
				description: vitrine.description,
				status: vitrine.status,
				visibility: vitrine.visibility,
				bannerImage: vitrine.bannerImage,
				createdAt: vitrine.createdAt.toISOString(),
				updatedAt: vitrine.updatedAt.toISOString(),
				organization: vitrine.organization,
				sections: vitrine.sections.map((section) => ({
					id: section.id,
					title: section.title,
					subtitle: section.subtitle,
					description: section.description,
					position: section.position,
					isLocked: section.isLocked,
					requiresPurchase: section.requiresPurchase,
					checkoutUrl: section.checkoutUrl,
					price: section.price ? Number(section.price) : null,
					originalPrice: section.originalPrice ? Number(section.originalPrice) : null,
					accessType: section.accessType,
					visibility: section.visibility,
					courses: section.courses.map((courseRelation) => ({
						id: courseRelation.course.id,
						name: courseRelation.course.name,
						logo: courseRelation.course.logo,
						community: courseRelation.course.community,
						link: courseRelation.course.link,
						createdAt: courseRelation.course.createdAt.toISOString(),
						updatedAt: courseRelation.course.updatedAt.toISOString(),
					})),
				})),
			}));

			return c.json(formattedVitrines);
		} catch (error) {
			console.error("Error fetching public vitrines:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);