import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";

const querySchema = z.object({
    organizationSlug: z.string(),
});

export const testMembership = new Hono()
    .use(authMiddleware)
    .get("/test-membership", validator("query", querySchema), async (c) => {
        const { organizationSlug } = c.req.valid("query");
        const user = c.get("user");

        // Buscar organização
        const org = await db.organization.findUnique({
            where: { slug: organizationSlug }
        });

        if (!org) {
            return c.json({ error: "Org not found" });
        }

        // Buscar membership
        const membership = await db.member.findFirst({
            where: {
                userId: user.id,
                organizationId: org.id,
            }
        });

        // Buscar todos os membros da org
        const allMembers = await db.member.findMany({
            where: { organizationId: org.id },
            include: { user: { select: { email: true } } }
        });

        return c.json({
            user: { id: user.id, email: user.email },
            organization: { id: org.id, slug: org.slug },
            membership: membership,
            allMembers: allMembers.map(m => ({
                userId: m.userId,
                email: m.user.email,
                role: m.role
            })),
            query: {
                userId: user.id,
                organizationId: org.id
            }
        });
    });