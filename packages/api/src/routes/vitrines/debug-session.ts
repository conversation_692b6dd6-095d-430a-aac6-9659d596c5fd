import { auth } from "@repo/auth";
import { Hono } from "hono";

export const debugSession = new Hono()
    .get("/debug-session", async (c) => {
        try {
            console.log("🔍 Debug Session - Starting manual session test");
            console.log("🔍 Debug Session - URL:", c.req.url);
            
            // Log all headers
            const headers = Object.fromEntries(c.req.raw.headers.entries());
            console.log("🔍 Debug Session - Headers:", headers);

            // Test session extraction with different approaches
            console.log("🔍 Debug Session - Testing auth.api.getSession()...");
            
            const session = await auth.api.getSession({
                headers: c.req.raw.headers,
            });

            console.log("🔍 Debug Session - Session result:", {
                hasSession: !!session,
                userId: session?.user?.id,
                userEmail: session?.user?.email,
                userName: session?.user?.name,
                sessionId: session?.session?.id,
                sessionToken: session?.session?.token ? "***" + session.session.token.slice(-4) : null,
                sessionExpiresAt: session?.session?.expiresAt
            });

            // Additional debugging - check cookie parsing
            const cookieHeader = c.req.header("cookie");
            console.log("🔍 Debug Session - Cookie header:", cookieHeader);
            
            if (cookieHeader) {
                const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
                    const [key, value] = cookie.trim().split('=');
                    acc[key] = value;
                    return acc;
                }, {} as Record<string, string>);
                console.log("🔍 Debug Session - Parsed cookies:", Object.keys(cookies));
                console.log("🔍 Debug Session - Auth cookie present:", !!cookies['better-auth.session_token']);
            }

            return c.json({
                success: true,
                hasSession: !!session,
                user: session?.user ? {
                    id: session.user.id,
                    email: session.user.email,
                    name: session.user.name,
                    role: session.user.role
                } : null,
                session: session?.session ? {
                    id: session.session.id,
                    token: session.session.token ? "***" + session.session.token.slice(-4) : null,
                    expiresAt: session.session.expiresAt
                } : null,
                headers: {
                    cookie: c.req.header("cookie"),
                    authorization: c.req.header("authorization"),
                    origin: c.req.header("origin"),
                    referer: c.req.header("referer")
                },
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error("❌ Debug Session Error:", error);
            return c.json({ 
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
                stack: error instanceof Error ? error.stack : undefined,
                timestamp: new Date().toISOString()
            }, 500);
        }
    });