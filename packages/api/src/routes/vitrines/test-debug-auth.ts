import { db } from "@repo/database";
import { Hono } from "hono";
import { debugAuthMiddleware } from "../../middleware/debug-auth";

export const testDebugAuth = new Hono()
	.use(debugAuthMiddleware)
	.get("/test-debug-auth", async (c) => {
		try {
			const user = c.get("user");

			console.log("🔍 Test Debug Auth - User from context:", user?.email, user?.id);

			// Verificar se o usuário existe no banco
			const dbUser = await db.user.findUnique({
				where: { id: user.id },
				select: { id: true, email: true, name: true }
			});

			console.log("🔍 Test Debug Auth - User from DB:", dbUser?.email, dbUser?.id);

			// Verificar membroships do usuário
			const memberships = await db.member.findMany({
				where: { userId: user.id },
				include: {
					organization: {
						select: { id: true, name: true, slug: true }
					}
				}
			});

			console.log("🔍 Test Debug Auth - Memberships:", memberships.length);
			memberships.forEach(m => {
				console.log(`   - ${m.organization.name} (${m.organization.slug}): ${m.role}`);
			});

			return c.json({
				message: "Debug auth test successful",
				user: dbUser,
				memberships: memberships.map(m => ({
					organization: m.organization,
					role: m.role
				}))
			});
		} catch (error) {
			console.error("Error in test debug auth:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});