import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { authMiddleware } from "../../middleware/auth";

const querySchema = z.object({
    organizationSlug: z.string(),
});

export const debugMembership = new Hono()
    .use(authMiddleware)
    .get("/debug-membership", validator("query", querySchema), async (c) => {
        try {
            const { organizationSlug } = c.req.valid("query");
            const user = c.get("user");

            console.log("🔍 Debug Membership - User:", user);
            console.log("🔍 Debug Membership - Organization Slug:", organizationSlug);

            // Buscar organização
            const organization = await db.organization.findUnique({
                where: { slug: organizationSlug },
            });

            console.log("🔍 Debug Membership - Organization:", organization);

            if (!organization) {
                return c.json({ error: "Organization not found" }, 404);
            }

            // Buscar membership diretamente
            const membership = await db.member.findFirst({
                where: {
                    userId: user.id,
                    organizationId: organization.id,
                },
            });

            console.log("🔍 Debug Membership - Direct query result:", membership);

            // Buscar todos os membros da organização
            const allMembers = await db.member.findMany({
                where: { organizationId: organization.id },
                include: { user: true },
            });

            console.log("🔍 Debug Membership - All members:", allMembers);

            // Buscar todas as memberships do usuário
            const userMemberships = await db.member.findMany({
                where: { userId: user.id },
                include: { organization: true },
            });

            console.log("🔍 Debug Membership - User memberships:", userMemberships);

            return c.json({
                user: {
                    id: user.id,
                    email: user.email,
                    name: user.name,
                },
                organization: {
                    id: organization.id,
                    name: organization.name,
                    slug: organization.slug,
                },
                membership: membership,
                allOrgMembers: allMembers.map(m => ({
                    id: m.id,
                    userId: m.userId,
                    userEmail: m.user.email,
                    role: m.role,
                })),
                allUserMemberships: userMemberships.map(m => ({
                    id: m.id,
                    organizationId: m.organizationId,
                    organizationSlug: m.organization.slug,
                    role: m.role,
                })),
                debug: {
                    userIdType: typeof user.id,
                    orgIdType: typeof organization.id,
                    userIdLength: user.id.length,
                    orgIdLength: organization.id.length,
                }
            });

        } catch (error) {
            console.error("❌ Debug Membership Error:", error);
            return c.json({ error: error.message }, 500);
        }
    });