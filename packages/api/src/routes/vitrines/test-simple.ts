import { Hono } from "hono";
import { authMiddleware } from "../../middleware/auth";

export const testSimple = new Hono()
	.use(authMiddleware)
	.get("/test-simple", async (c) => {
		try {
			const user = c.get("user");
			console.log("🔍 Test Simple - User from authMiddleware:", user?.email, user?.id);

			return c.json({
				message: "Simple auth test successful",
				user: {
					id: user.id,
					email: user.email,
					name: user.name
				}
			});
		} catch (error) {
			console.error("Error in test simple:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
