import { db } from "@repo/database";
import { Hono } from "hono";
import { authMiddleware } from "../../middleware/auth";

export const testAuth = new Hono()
	.use(authMiddleware)
	.get("/test-auth", async (c) => {
		try {
			const user = c.get("user");

			console.log("🔍 Test Auth - User from session:", user?.email, user?.id);

			// Verificar se o usuário existe no banco
			const dbUser = await db.user.findUnique({
				where: { id: user.id },
				select: { id: true, email: true, name: true }
			});

			console.log("🔍 Test Auth - User from DB:", dbUser?.email, dbUser?.id);

			// Verificar membroships do usuário
			const memberships = await db.member.findMany({
				where: { userId: user.id },
				include: {
					organization: {
						select: { id: true, name: true, slug: true }
					}
				}
			});

			console.log("🔍 Test Auth - Memberships:", memberships.length);
			memberships.forEach(m => {
				console.log(`   - ${m.organization.name} (${m.organization.slug}): ${m.role}`);
			});

			return c.json({
				user: dbUser,
				memberships: memberships.map(m => ({
					organization: m.organization,
					role: m.role
				}))
			});
		} catch (error) {
			console.error("Error in test auth:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
