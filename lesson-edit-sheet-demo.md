# 🎥 LessonEditSheet - Editor Lateral de Aulas

## 📋 Visão Geral

Implementei um **sheet lateral completo** para edição detalhada das aulas, seguindo o padrão visual da aplicação principal da Cakto mostrado na imagem de referência.

## ✨ Características Principais

### 🎯 **Design Idêntico ao Padrão Cakto**
- Sheet lateral que abre do lado direito
- Layout exatamente como mostrado na imagem de referência
- Seções organizadas: Detalhes, Vídeo, Arquivos
- Editor de conteúdo integrado

### 🎬 **Upload e Gerenciamento de Vídeo**
- **Drag & Drop**: Arraste vídeos diretamente
- **Preview em Tempo Real**: Visualização do vídeo carregado
- **Formatos Suportados**: MP4, MOV, AVI, MKV, WebM
- **Progress Bar**: Indicador de progresso durante upload
- **URL Externa**: Opção para usar links do YouTube/Vimeo

### 🖼️ **Gerenciamento de Thumbnail**
- **Upload Automático**: Thumbnail gerado automaticamente
- **Upload Manual**: Possibilidade de enviar thumbnail personalizado
- **Recomendação**: Badge informando tamanho recomendado (800x450px)
- **Preview**: Visualização da thumbnail em tempo real

### ✍️ **Editor de Conteúdo Rico**
- **Toolbar Completa**: Negrito, Itálico, Sublinhado, Listas
- **Área de Escrita**: Editor de texto estilo "Write something awesome..."
- **HTML Output**: Geração automática de conteúdo HTML

### 📁 **Gerenciamento de Arquivos**
- **Múltiplos Formatos**: PDF, DOC, PPT, Imagens, TXT
- **Organização**: Arquivos organizados por aula
- **Status Tracking**: Acompanhamento de status de upload

## 🏗️ Integração com CourseStructureForm

### **Antes (Dialog Simples)**
```typescript
// Apenas campos básicos
- Nome da aula
- Descrição
- Duração
```

### **Depois (Sheet Completo)**
```typescript
// Editor completo
✅ Nome da aula
✅ Duração com seletor
✅ Upload de vídeo com preview
✅ Gerenciamento de thumbnail
✅ Editor de conteúdo rico
✅ Tabs organizadas (Detalhes/Vídeo/Arquivos)
✅ Ações de salvar/excluir
```

## 🔧 Como Funciona

### **1. Abertura do Sheet**
```typescript
// Ao clicar em "Adicionar Aula" ou no ícone de edição
openCreateLessonSheet(moduleId) // Nova aula
openEditLessonSheet(moduleId, lesson) // Editar existente
```

### **2. Estrutura de Dados**
```typescript
interface CourseLesson {
  id: string;
  name: string;
  description?: string;
  duration?: string;
  position: number;
  videoUrl?: string;        // ✨ Novo
  thumbnail?: string;       // ✨ Novo
  files?: CourseLessonFile[]; // ✨ Novo
}
```

### **3. Upload de Vídeo**
```typescript
// Upload com progresso em tempo real
const handleVideoUpload = async (file: File) => {
  // Simula upload com progress bar
  const url = await simulateUpload(setVideoUpload, file);
  // Atualiza URL do vídeo
  handleInputChange('videoUrl', url);
};
```

### **4. Gestão de Estado**
```typescript
// Estado do sheet
const [isLessonSheetOpen, setIsLessonSheetOpen] = useState(false);
const [editingLesson, setEditingLesson] = useState(null);

// Upload progress
const [videoUpload, setVideoUpload] = useState({
  progress: 0,
  status: 'idle'
});
```

## 🎨 Interface Visual

### **Header do Sheet**
```
┌─────────────────────────────────────────────┐
│ [✖] Editar Aula                             │
│     Configure o conteúdo da aula            │
└─────────────────────────────────────────────┘
```

### **Card de Informações Rápidas**
```
┌─────────────────────────────────────────────┐
│ [🎬] Nome da aula___________________        │
│     Imediatamente ▼                         │
└─────────────────────────────────────────────┘
```

### **Tabs de Navegação**
```
┌─────────────────────────────────────────────┐
│ [Detalhes] [Vídeo] [Arquivos]              │
└─────────────────────────────────────────────┘
```

### **Seção de Vídeo**
```
┌─────────────────────────────────────────────┐
│ 🎬 Vídeo                                    │
│ ┌─────────────────────────────────────────┐ │
│ │  [Clique para adicionar vídeo]          │ │
│ │  Formatos: MP4, MOV, AVI • Max: 2GB     │ │
│ └─────────────────────────────────────────┘ │
│ ████████████████░░░░ 80% Enviando...        │
└─────────────────────────────────────────────┘
```

### **Editor de Conteúdo**
```
┌─────────────────────────────────────────────┐
│ Editor de conteúdo                          │
│ [B] [I] [U] | [•] [1.]                     │
│ ┌─────────────────────────────────────────┐ │
│ │ Write something awesome...               │ │
│ │                                         │ │
│ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────┘
```

### **Rodapé de Ações**
```
┌─────────────────────────────────────────────┐
│ [Excluir]              [Cancelar] [💾 Salvar]│
└─────────────────────────────────────────────┘
```

## 🚀 Benefícios da Implementação

### **Para o Usuário**
- ✅ **Interface Familiar**: Igual ao padrão já usado na Cakto
- ✅ **Workflow Eficiente**: Tudo em uma tela lateral
- ✅ **Upload Visual**: Progress bars e previews
- ✅ **Organização Clara**: Tabs separando conteúdo

### **Para o Desenvolvedor**
- ✅ **Componente Reutilizável**: Pode ser usado em outros contextos
- ✅ **Estado Gerenciado**: Upload progress e validações
- ✅ **TypeScript Completo**: Tipagem robusta
- ✅ **Padrão Consistente**: Segue design system da aplicação

## 📱 Como Usar

### **1. No CourseStructureForm**
```typescript
// Expandir um módulo e clicar em "Adicionar Aula"
// O sheet abre lateralmente com todos os campos

// Ou clicar no ícone de edição de uma aula existente
// O sheet abre com os dados preenchidos
```

### **2. Upload de Vídeo**
```typescript
// Arrastar arquivo para a área de upload
// OU clicar na área e selecionar arquivo
// Progress bar mostra o progresso
// Preview aparece quando completo
```

### **3. Gerenciar Thumbnail**
```typescript
// Upload automático após vídeo
// OU upload manual de imagem personalizada
// Visualização em tempo real
```

### **4. Editor de Conteúdo**
```typescript
// Usar toolbar para formatação
// Escrever conteúdo da aula
// HTML gerado automaticamente
```

## 🔗 Integração Completa

O LessonEditSheet está **totalmente integrado** com o CourseStructureForm:

- ✅ **Abertura**: Botões direcionam para o sheet
- ✅ **Salvamento**: Dados sincronizam com o módulo
- ✅ **Exclusão**: Remove aula do módulo
- ✅ **Estado**: Mantém dados consistentes
- ✅ **Validação**: Campos obrigatórios validados

## 🎯 Resultado Final

Agora, ao criar/editar aulas, o usuário tem:

1. **Sheet lateral** igual ao padrão Cakto
2. **Upload de vídeo** com preview e progress
3. **Gerenciamento de thumbnail** completo
4. **Editor de conteúdo** rico e funcional
5. **Organização em tabs** clara e intuitiva
6. **Integração perfeita** com o fluxo de criação

O resultado é uma experiência de edição de aulas **profissional** e **consistente** com o padrão visual já estabelecido na aplicação principal da Cakto! 🎉
