# 🚀 Members-Base - Configuração Local

Este guia te ajudará a configurar e executar o projeto **Members-Base** localmente.

## 📋 Pré-requisitos

- **Node.js** 20+
- **pnpm** 9.3.0+
- **Docker** e **Docker Compose**
- **Git**

## 🛠️ Configuração Inicial

### 1. Configurar Variáveis de Ambiente

```bash
# Copiar arquivo de exemplo
cp env.local.example .env.local

# Editar o arquivo conforme necessário
nano .env.local
```

### 2. Configurações Mínimas Necessárias

Para começar rapidamente, você só precisa das seguintes variáveis no `.env.local`:

```env
# DATABASE
DATABASE_URL="postgresql://cakto:cakto@localhost:5432/cakto"

# BETTER AUTH
BETTER_AUTH_SECRET="112c209614f7bb08cf834f53d5e2b1721ba940a106b01a3769a4e9bd29cd723c697ce245a874c1ab3140142c3a4fb780c19bf1c5a302b1ee3e85a4950cc3e6c8"

# NEXT.JS
NEXT_PUBLIC_SITE_URL="http://localhost:3000"
PORT=3000

# AMBIENTE
NODE_ENV="development"
```

## 🚀 Inicialização Rápida

### Opção 1: Script Automatizado (Recomendado)

```bash
# Executar script de inicialização
./start-local.sh
```

### Opção 2: Passo a Passo Manual

```bash
# 1. Iniciar serviços do Docker
docker-compose up -d

# 2. Instalar dependências
pnpm install

# 3. Gerar tipos do Prisma
cd packages/database
pnpm generate
cd ../..

# 4. Executar migrações
cd packages/database
pnpm push
cd ../..

# 5. Iniciar servidor de desenvolvimento
pnpm dev
```

## 🌐 Acessos

- **Aplicação**: http://localhost:3000
- **Prisma Studio**: http://localhost:5555 (execute `cd packages/database && pnpm studio`)

## 📁 Estrutura do Projeto

```
members-base/
├── apps/
│   └── web/                 # Aplicação Next.js principal
├── packages/
│   ├── auth/               # Configuração Better Auth
│   ├── database/           # Schema Prisma e configurações
│   ├── api/                # Rotas da API
│   ├── mail/               # Configuração de email
│   ├── payments/           # Integrações de pagamento
│   ├── storage/            # Configuração de storage
│   └── utils/              # Utilitários
├── config/                 # Configurações globais
├── docker-compose.yml      # Serviços Docker (PostgreSQL + Redis)
└── .env.local             # Variáveis de ambiente
```

## 🔧 Configurações Opcionais

### Autenticação Social

Para habilitar login com Google/GitHub:

```env
GOOGLE_CLIENT_ID="seu_google_client_id"
GOOGLE_CLIENT_SECRET="seu_google_client_secret"

GITHUB_CLIENT_ID="seu_github_client_id"
GITHUB_CLIENT_SECRET="seu_github_client_secret"
```

### Email

Para envio de emails (magic links, verificação, etc.):

```env
# Opção 1: Nodemailer (desenvolvimento)
MAIL_HOST="localhost"
MAIL_PORT="1025"
MAIL_USER=""
MAIL_PASS=""

# Opção 2: Resend (recomendado)
RESEND_API_KEY="seu_resend_api_key"
```

### Storage (Upload de Arquivos)

Para upload de avatares e outros arquivos:

```env
# MinIO local ou S3
S3_ENDPOINT="http://localhost:9000"
S3_REGION="us-east-1"
S3_ACCESS_KEY_ID="minioadmin"
S3_SECRET_ACCESS_KEY="minioadmin"
NEXT_PUBLIC_AVATARS_BUCKET_NAME="avatars"
```

### Pagamentos

Para integração com Stripe:

```env
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."
NEXT_PUBLIC_PRICE_ID_PRO_MONTHLY="price_..."
NEXT_PUBLIC_PRICE_ID_PRO_YEARLY="price_..."
NEXT_PUBLIC_PRICE_ID_LIFETIME="price_..."
```

## 🐛 Solução de Problemas

### Erro de Conexão com Banco

```bash
# Verificar se PostgreSQL está rodando
docker-compose ps

# Reiniciar serviços
docker-compose down
docker-compose up -d
```

### Erro de Dependências

```bash
# Limpar cache do pnpm
pnpm store prune

# Reinstalar dependências
rm -rf node_modules
pnpm install
```

### Erro de Migrações

```bash
# Resetar banco de dados
cd packages/database
pnpm push --force-reset
cd ../..
```

## 📚 Comandos Úteis

```bash
# Desenvolvimento
pnpm dev                    # Iniciar servidor de desenvolvimento
pnpm build                  # Build de produção
pnpm start                  # Iniciar servidor de produção

# Banco de dados
cd packages/database
pnpm studio                 # Abrir Prisma Studio
pnpm push                   # Sincronizar schema
pnpm migrate                # Executar migrações

# Linting e formatação
pnpm lint                   # Executar linter
pnpm format                 # Formatar código

# Limpeza
pnpm clean                  # Limpar builds
```

## 🔒 Segurança

- **Nunca** commite o arquivo `.env.local`
- Use secrets diferentes para cada ambiente
- O `BETTER_AUTH_SECRET` deve ser único e seguro

## 📞 Suporte

Para dúvidas ou problemas:

1. Verifique a [documentação oficial](https://cakto.com/docs)
2. Consulte os logs do Docker: `docker-compose logs`
3. Verifique os logs do Next.js no terminal

---

**Happy coding! 🎉**
