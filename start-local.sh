#!/bin/bash

# =============================================================================
# SCRIPT DE INICIALIZAÇÃO LOCAL - MEMBERS-BASE
# =============================================================================

set -e

echo "🚀 Iniciando Members-Base localmente..."

# Verificar se o Docker está rodando
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker não está rodando. Por favor, inicie o Docker primeiro."
    exit 1
fi

# Verificar se o arquivo .env.local existe
if [ ! -f ".env.local" ]; then
    echo "📝 Arquivo .env.local não encontrado. Copiando do exemplo..."
    cp env.local.example .env.local
    echo "✅ Arquivo .env.local criado. Edite conforme necessário."
fi

# Iniciar serviços do Docker Compose
echo "🐳 Iniciando PostgreSQL e Redis..."
docker-compose up -d

# Aguardar PostgreSQL estar pronto
echo "⏳ Aguardando PostgreSQL estar pronto..."
until docker-compose exec -T postgres pg_isready -U cakto; do
    echo "Aguardando PostgreSQL..."
    sleep 2
done

# Instalar dependências
echo "📦 Instalando dependências..."
pnpm install

# Gerar tipos do Prisma
echo "🔧 Gerando tipos do Prisma..."
cd packages/database
pnpm generate
cd ../..

# Executar migrações do banco de dados
echo "🗄️ Executando migrações do banco de dados..."
cd packages/database
pnpm push
cd ../..

# Iniciar o servidor de desenvolvimento
echo "🌐 Iniciando servidor de desenvolvimento..."
pnpm dev

echo "✅ Members-Base iniciado com sucesso!"
echo "📱 Acesse: http://localhost:3000"
echo "🗄️ Prisma Studio: http://localhost:5555 (execute: cd packages/database && pnpm studio)"
