# Webhook Integration - Cakto to Members-base

## Overview

This integration automatically provisions user accounts and course access in the Members-base system when purchases are completed in the Cakto backend.

## Flow

1. **Purchase Completed** in Cakto backend
2. **Webhook Sent** to Members-base endpoint
3. **User Account** created/updated in Members-base
4. **Course Access** granted based on product association
5. **Email Notifications** sent to user

## Configuration

### Environment Variables

```env
CAKTO_WEBHOOK_SECRET=your_webhook_secret_here
NEXT_PUBLIC_APP_URL=https://members.cakto.com.br
```

### Cakto Backend Configuration

```env
MEMBERS_WEBHOOK_URL=https://members.cakto.com.br/webhooks/cakto/purchase
MEMBERS_WEBHOOK_SECRET=your_webhook_secret_here
```

## Webhook Payload

```json
{
  "id": "order_id",
  "customer": {
    "name": "Customer Name",
    "email": "<EMAIL>",
    "phone": "***********",
    "docNumber": "***********"
  },
  "product": {
    "name": "Product Name",
    "id": "product_id",
    "short_id": "PROD123"
  },
  "status": "approved",
  "amount": 99.90,
  "paymentMethod": "credit_card",
  "paidAt": "2024-01-01T10:00:00Z",
  "createdAt": "2024-01-01T09:00:00Z"
}
```

## Security

- HMAC SHA-256 signature verification
- Secret token validation
- Payload validation with Zod schemas

## Testing

Run the test script:

```bash
pnpm tsx scripts/test-cakto-webhook-integration.ts
```

## Course Product Association

Before the webhook can grant course access, you need to associate Cakto products with courses:

```sql
INSERT INTO "CourseProduct" ("id", "courseId", "caktoProductId")
VALUES (gen_random_uuid(), 'course_id_here', 'cakto_product_id_here');
```

## Email Templates

- `userCreated`: Welcome email for new users with login credentials
- `courseAccess`: Course access notification for all users

## Error Handling

- Invalid signatures return 401
- Missing course associations are logged as errors
- Email failures are logged but don't block the process
- All errors are logged with structured data