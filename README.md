# 🚀 Cakto Members

Uma plataforma SaaS moderna e completa para gerenciamento de membros, construída com Next.js 15, Better Auth, e arquitetura de monorepo.

## ✨ Características

- 🔐 **Autenticação Completa** - Better Auth com múltiplos provedores
- 🏢 **Gestão de Organizações** - Multi-tenant com controle de membros
- 💳 **Sistema de Pagamentos** - Integração com Stripe, LemonSqueezy e outros
- 📧 **Sistema de Email** - Templates responsivos com múltiplos provedores
- 🎨 **UI Moderna** - Tailwind CSS + shadcn/ui
- 🌍 **Internacionalização** - Suporte a múltiplos idiomas
- 🔒 **Segurança** - 2FA, passkeys, sessões seguras
- 📱 **Responsivo** - Design mobile-first
- ⚡ **Performance** - Next.js 15 com App Router
- 🗄️ **Banco de Dados** - PostgreSQL com Prisma ORM

## 🛠️ Stack Tecnológica

### Frontend
- **Next.js 15** - Framework React com App Router
- **TypeScript** - Tipagem estática
- **Tailwind CSS** - Framework CSS utilitário
- **shadcn/ui** - Componentes React modernos
- **TanStack Query** - Gerenciamento de estado do servidor

### Backend
- **Better Auth** - Sistema de autenticação completo
- **Prisma** - ORM para PostgreSQL
- **Fastify** - Framework web rápido
- **Zod** - Validação de schemas

### Infraestrutura
- **PostgreSQL** - Banco de dados principal
- **Redis** - Cache e sessões
- **Docker** - Containerização
- **Turbo** - Build system para monorepo

## 🚀 Início Rápido

### Pré-requisitos

- **Node.js** 20+
- **pnpm** 9.3.0+
- **Docker** e **Docker Compose**
- **Git**

### Instalação

1. **Clone o repositório**
   ```bash
   git clone https://github.com/ismaelmcosta/super-members.git
   cd super-members
   ```

2. **Configure as variáveis de ambiente**
   ```bash
   cp env.local.example .env.local
   # Edite o arquivo .env.local conforme necessário
   ```

3. **Execute o script de inicialização**
   ```bash
   ./start-local.sh
   ```

4. **Acesse a aplicação**
   - **Frontend**: http://localhost:3000
   - **Prisma Studio**: http://localhost:5555

## 📁 Estrutura do Projeto

```
super-members/
├── apps/
│   └── web/                    # Aplicação Next.js principal
├── packages/
│   ├── auth/                   # Configuração Better Auth
│   ├── database/               # Schema Prisma e queries
│   ├── api/                    # Rotas da API
│   ├── mail/                   # Sistema de email
│   ├── payments/               # Integrações de pagamento
│   ├── storage/                # Upload de arquivos
│   ├── i18n/                   # Internacionalização
│   ├── logs/                   # Sistema de logs
│   ├── utils/                  # Utilitários
│   └── ai/                     # Integração com IA
├── config/                     # Configurações globais
├── tooling/                    # Ferramentas de desenvolvimento
└── docker-compose.yml          # Serviços Docker
```

## 🔧 Configuração

### Variáveis de Ambiente Essenciais

```env
# Banco de Dados
DATABASE_URL="postgresql://cakto:cakto@localhost:5432/cakto"

# Autenticação
BETTER_AUTH_SECRET="seu_secret_aqui"

# Aplicação
NEXT_PUBLIC_SITE_URL="http://localhost:3000"
PORT=3000
NODE_ENV="development"
```

### Configurações Opcionais

- **Autenticação Social**: Google, GitHub
- **Email**: Resend, Nodemailer, Mailgun
- **Storage**: S3, MinIO
- **Pagamentos**: Stripe, LemonSqueezy, Polar
- **Analytics**: Google Analytics, PostHog, Mixpanel

## 🎯 Funcionalidades Principais

### 👥 Gestão de Usuários
- Registro e login com múltiplos métodos
- Perfis de usuário personalizáveis
- Autenticação de dois fatores (2FA)
- Passkeys para login sem senha
- Gerenciamento de sessões

### 🏢 Organizações
- Criação e gestão de organizações
- Sistema de convites por email
- Controle de permissões por membro
- Logotipos e personalização
- Dashboard organizacional

### 💳 Pagamentos
- Planos de assinatura flexíveis
- Integração com múltiplos gateways
- Portal do cliente
- Gestão de assinaturas
- Webhooks para sincronização

### 📧 Comunicação
- Templates de email responsivos
- Magic links para login
- Verificação de email
- Notificações de organização
- Newsletter integrada

### 🔒 Segurança
- Sessões seguras com cookies
- Proteção CSRF
- Rate limiting
- Validação de dados com Zod
- Logs de auditoria

## 🚀 Deploy

### Desenvolvimento Local
```bash
# Iniciar serviços
docker-compose up -d

# Instalar dependências
pnpm install

# Executar migrações
cd packages/database && pnpm push

# Iniciar servidor
pnpm dev
```

### Produção
```bash
# Build da aplicação
pnpm build

# Iniciar servidor de produção
pnpm start
```

## 📚 Comandos Úteis

```bash
# Desenvolvimento
pnpm dev                    # Servidor de desenvolvimento
pnpm build                  # Build de produção
pnpm start                  # Servidor de produção

# Banco de dados
cd packages/database
pnpm studio                 # Prisma Studio
pnpm push                   # Sincronizar schema
pnpm migrate                # Executar migrações

# Qualidade de código
pnpm lint                   # Executar linter
pnpm format                 # Formatar código

# Limpeza
pnpm clean                  # Limpar builds
```

## 🤝 Contribuindo

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

## 🆘 Suporte

- 📖 [Documentação](https://cakto.com/docs)
- 🐛 [Issues](https://github.com/ismaelmcosta/super-members/issues)
- 💬 [Discussions](https://github.com/ismaelmcosta/super-members/discussions)

## 🙏 Agradecimentos

- [Cakto Members](https://cakto.com) - Template base
- [Better Auth](https://better-auth.com) - Sistema de autenticação
- [shadcn/ui](https://ui.shadcn.com) - Componentes UI
- [Vercel](https://vercel.com) - Deploy e infraestrutura

---

**Desenvolvido com ❤️ por [Ismael Costa](https://github.com/ismaelmcosta)**
